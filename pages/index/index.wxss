/* pages/index/index.wxss */
.index-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏样式 */
.search-section {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 20rpx;
  height: 80rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.search-icon {
  color: #fff;
  font-size: 32rpx;
}

/* 搜索历史样式 */
.search-history {
  position: absolute;
  top: 120rpx;
  left: 20rpx;
  right: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400rpx;
  overflow-y: auto;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.history-clear {
  font-size: 24rpx;
  color: #999;
}

.history-list {
  padding: 20rpx;
}

.history-item {
  display: inline-block;
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  margin: 0 12rpx 12rpx 0;
}

/* 热门关键词样式 */
.hot-keywords-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.keyword-item {
  background-color: #f0f8ff;
  color: #1890ff;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: 1rpx solid #d6e4ff;
}

/* 分类样式 */
.categories-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #fafafa;
}

.category-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.category-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 标签样式 */
.tabs-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.tabs-header {
  display: flex;
  padding: 0 20rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-active {
  color: #1890ff;
  font-weight: 600;
}

.tab-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
}

/* 资源列表样式 */
.resources-section {
  background-color: #fff;
}

.resource-list {
  padding: 0 20rpx;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.resource-item:last-child {
  border-bottom: none;
}

.resource-info {
  flex: 1;
  margin-right: 20rpx;
}

.resource-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resource-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resource-meta {
  display: flex;
  gap: 20rpx;
}

.meta-item {
  font-size: 20rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.resource-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  background-color: #1890ff;
  color: #fff;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 20rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
