<!--pages/index/index.wxml-->
<view class="index-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input
        class="search-input"
        placeholder="搜索夸克网盘、阿里云盘资源"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindfocus="onSearchFocus"
        bindblur="onSearchBlur"
        bindconfirm="handleSearch"
      />
      <view class="search-btn" bindtap="handleSearch">
        <text class="search-icon">🔍</text>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="search-history" wx:if="{{showSearchHistory && searchHistory.length > 0}}">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <text class="history-clear" bindtap="clearSearchHistory">清空</text>
      </view>
      <view class="history-list">
        <text
          class="history-item"
          wx:for="{{searchHistory}}"
          wx:key="index"
          data-keyword="{{item}}"
          bindtap="onSearchHistoryTap"
        >{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 热门关键词 -->
  <view class="hot-keywords-section" wx:if="{{hotKeywords.length > 0}}">
    <view class="section-header">
      <text class="section-title">🔥 热门搜索</text>
    </view>
    <view class="keywords-list">
      <text
        class="keyword-item"
        wx:for="{{hotKeywords}}"
        wx:key="id"
        data-keyword="{{item.keyword}}"
        bindtap="onHotKeywordTap"
      >{{item.keyword}}</text>
    </view>
  </view>

  <!-- 分类导航 -->
  <view class="categories-section" wx:if="{{categories.length > 0}}">
    <view class="section-header">
      <text class="section-title">📂 资源分类</text>
    </view>
    <view class="categories-grid">
      <view
        class="category-item"
        wx:for="{{categories}}"
        wx:key="id"
        data-category="{{item}}"
        bindtap="onCategoryTap"
      >
        <text class="category-icon">{{item.icon}}</text>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 内容标签 -->
  <view class="tabs-section">
    <view class="tabs-header">
      <text
        class="tab-item {{currentTab === index ? 'tab-active' : ''}}"
        wx:for="{{tabList}}"
        wx:key="key"
        data-index="{{index}}"
        bindtap="onTabChange"
      >{{item.name}}</text>
    </view>
  </view>

  <!-- 资源列表 -->
  <view class="resources-section">
    <!-- 推荐资源 -->
    <view class="resource-list" wx:if="{{currentTab === 0}}">
      <view
        class="resource-item"
        wx:for="{{recommendedResources}}"
        wx:key="id"
        data-resource="{{item}}"
        bindtap="onResourceTap"
      >
        <view class="resource-info">
          <text class="resource-title">{{item.title}}</text>
          <text class="resource-desc">{{item.description}}</text>
          <view class="resource-meta">
            <text class="meta-item">{{item.diskType}}</text>
            <text class="meta-item">{{item.fileSize}}</text>
            <text class="meta-item">{{item.updateTime}}</text>
          </view>
        </view>
        <view class="resource-actions">
          <text class="action-btn">查看</text>
        </view>
      </view>
    </view>

    <!-- 热门资源 -->
    <view class="resource-list" wx:if="{{currentTab === 1}}">
      <view
        class="resource-item"
        wx:for="{{hotResources}}"
        wx:key="id"
        data-resource="{{item}}"
        bindtap="onResourceTap"
      >
        <view class="resource-info">
          <text class="resource-title">{{item.title}}</text>
          <text class="resource-desc">{{item.description}}</text>
          <view class="resource-meta">
            <text class="meta-item">{{item.diskType}}</text>
            <text class="meta-item">{{item.fileSize}}</text>
            <text class="meta-item">浏览 {{item.viewCount}}</text>
          </view>
        </view>
        <view class="resource-actions">
          <text class="action-btn">查看</text>
        </view>
      </view>
    </view>

    <!-- 最新资源 -->
    <view class="resource-list" wx:if="{{currentTab === 2}}">
      <view
        class="resource-item"
        wx:for="{{latestResources}}"
        wx:key="id"
        data-resource="{{item}}"
        bindtap="onResourceTap"
      >
        <view class="resource-info">
          <text class="resource-title">{{item.title}}</text>
          <text class="resource-desc">{{item.description}}</text>
          <view class="resource-meta">
            <text class="meta-item">{{item.diskType}}</text>
            <text class="meta-item">{{item.fileSize}}</text>
            <text class="meta-item">{{item.updateTime}}</text>
          </view>
        </view>
        <view class="resource-actions">
          <text class="action-btn">查看</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!isLoading && getCurrentTabResources().length === 0}}">
      <text class="empty-icon">📭</text>
      <text class="empty-text">暂无资源</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>
