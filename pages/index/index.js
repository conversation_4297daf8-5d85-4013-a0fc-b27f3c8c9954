// pages/index/index.js
const searchApi = require('../../api/search')
const resourceApi = require('../../api/resource')
const storageUtils = require('../../utils/storage')
const formatUtils = require('../../utils/format')
const app = getApp()

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    hotKeywords: [],
    searchHistory: [],
    showSearchHistory: false,
    
    // 推荐资源
    recommendedResources: [],
    hotResources: [],
    latestResources: [],
    
    // 分类
    categories: [],
    
    // 轮播图
    banners: [],
    
    // 加载状态
    isLoading: false,
    isRefreshing: false,
    
    // 页面状态
    currentTab: 0,
    tabList: [
      { name: '推荐', key: 'recommended' },
      { name: '热门', key: 'hot' },
      { name: '最新', key: 'latest' }
    ]
  },

  onLoad() {
    console.log('首页加载')
    this.initPage()
  },

  onShow() {
    console.log('首页显示')
    this.loadSearchHistory()
  },

  onPullDownRefresh() {
    this.refreshPage()
  },

  onReachBottom() {
    this.loadMoreResources()
  },

  // 初始化页面
  initPage() {
    this.setData({ isLoading: true })
    
    Promise.all([
      this.loadHotKeywords(),
      this.loadCategories(),
      this.loadBanners(),
      this.loadRecommendedResources()
    ]).finally(() => {
      this.setData({ isLoading: false })
    })
  },

  // 刷新页面
  refreshPage() {
    this.setData({ isRefreshing: true })
    
    this.initPage()
    
    setTimeout(() => {
      this.setData({ isRefreshing: false })
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 加载热门关键词
  loadHotKeywords() {
    return searchApi.getHotKeywords(10)
      .then((keywords) => {
        this.setData({ hotKeywords: keywords })
      })
      .catch((error) => {
        console.error('加载热门关键词失败:', error)
      })
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = storageUtils.getSearchHistory()
    this.setData({ searchHistory: history.slice(0, 8) })
  },

  // 加载分类
  loadCategories() {
    return resourceApi.getResourceCategories()
      .then((categories) => {
        this.setData({ categories: categories.slice(0, 8) })
      })
      .catch((error) => {
        console.error('加载分类失败:', error)
      })
  },

  // 加载轮播图
  loadBanners() {
    // 模拟轮播图数据
    const banners = [
      {
        id: 1,
        image: '/assets/images/banner1.jpg',
        title: '热门资源推荐',
        url: '/pages/search/search?keyword=热门'
      },
      {
        id: 2,
        image: '/assets/images/banner2.jpg',
        title: '最新资源上线',
        url: '/pages/search/search?keyword=最新'
      }
    ]
    this.setData({ banners })
    return Promise.resolve(banners)
  },

  // 加载推荐资源
  loadRecommendedResources() {
    return resourceApi.getRecommendedResources({ limit: 10 })
      .then((resources) => {
        this.setData({ recommendedResources: resources })
      })
      .catch((error) => {
        console.error('加载推荐资源失败:', error)
      })
  },

  // 加载热门资源
  loadHotResources() {
    return resourceApi.getHotResources({ limit: 10 })
      .then((resources) => {
        this.setData({ hotResources: resources })
      })
      .catch((error) => {
        console.error('加载热门资源失败:', error)
      })
  },

  // 加载最新资源
  loadLatestResources() {
    return resourceApi.getLatestResources({ limit: 10 })
      .then((resources) => {
        this.setData({ latestResources: resources })
      })
      .catch((error) => {
        console.error('加载最新资源失败:', error)
      })
  },

  // 加载更多资源
  loadMoreResources() {
    const { currentTab, tabList } = this.data
    const tabKey = tabList[currentTab].key
    
    // 根据当前标签加载更多数据
    switch (tabKey) {
      case 'recommended':
        this.loadMoreRecommended()
        break
      case 'hot':
        this.loadMoreHot()
        break
      case 'latest':
        this.loadMoreLatest()
        break
    }
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
  },

  // 搜索聚焦
  onSearchFocus() {
    this.setData({ showSearchHistory: true })
  },

  // 搜索失焦
  onSearchBlur() {
    setTimeout(() => {
      this.setData({ showSearchHistory: false })
    }, 200)
  },

  // 执行搜索
  handleSearch(keyword) {
    const searchKeyword = keyword || this.data.searchKeyword
    
    if (!searchKeyword.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }
    
    // 添加到搜索历史
    storageUtils.addSearchHistory(searchKeyword.trim())
    
    // 跳转到搜索结果页
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(searchKeyword.trim())}`
    })
  },

  // 点击热门关键词
  onHotKeywordTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.handleSearch(keyword)
  },

  // 点击搜索历史
  onSearchHistoryTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.handleSearch(keyword)
  },

  // 清空搜索历史
  clearSearchHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          storageUtils.clearSearchHistory()
          this.setData({ searchHistory: [] })
          wx.showToast({
            title: '已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  // 切换标签
  onTabChange(e) {
    const index = e.currentTarget.dataset.index
    const { currentTab, tabList } = this.data
    
    if (index === currentTab) return
    
    this.setData({ currentTab: index })
    
    const tabKey = tabList[index].key
    
    // 根据标签加载对应数据
    switch (tabKey) {
      case 'hot':
        if (this.data.hotResources.length === 0) {
          this.loadHotResources()
        }
        break
      case 'latest':
        if (this.data.latestResources.length === 0) {
          this.loadLatestResources()
        }
        break
    }
  },

  // 点击轮播图
  onBannerTap(e) {
    const banner = e.currentTarget.dataset.banner
    if (banner.url) {
      wx.navigateTo({
        url: banner.url
      })
    }
  },

  // 点击分类
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    wx.navigateTo({
      url: `/pages/search/search?category=${category.id}`
    })
  },

  // 点击资源
  onResourceTap(e) {
    const resource = e.currentTarget.dataset.resource
    wx.navigateTo({
      url: `/pages/detail/detail?id=${resource.id}`
    })
  },

  // 加载更多推荐资源
  loadMoreRecommended() {
    // TODO: 实现加载更多推荐资源
    console.log('加载更多推荐资源')
  },

  // 加载更多热门资源
  loadMoreHot() {
    // TODO: 实现加载更多热门资源
    console.log('加载更多热门资源')
  },

  // 加载更多最新资源
  loadMoreLatest() {
    // TODO: 实现加载更多最新资源
    console.log('加载更多最新资源')
  },

  // 获取当前标签的资源列表
  getCurrentTabResources() {
    const { currentTab, recommendedResources, hotResources, latestResources } = this.data
    switch (currentTab) {
      case 0:
        return recommendedResources
      case 1:
        return hotResources
      case 2:
        return latestResources
      default:
        return []
    }
  },

  // 格式化文件大小
  formatFileSize(size) {
    return formatUtils.formatFileSize(size)
  },

  // 格式化时间
  formatTime(time) {
    return formatUtils.formatRelativeTime(time)
  },

  // 格式化网盘类型
  formatDiskType(type) {
    return formatUtils.formatDiskType(type)
  }
})
