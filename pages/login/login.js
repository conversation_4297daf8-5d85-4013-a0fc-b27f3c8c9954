// pages/login/login.js
const authUtils = require('../../utils/auth')
const storageUtils = require('../../utils/storage')
const app = getApp()

Page({
  data: {
    isLoading: false,
    canIUseGetUserProfile: false,
    canIUsePhoneNumber: false
  },

  onLoad() {
    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    
    // 检查是否支持getPhoneNumber
    if (wx.getPhoneNumber) {
      this.setData({
        canIUsePhoneNumber: true
      })
    }
    
    // 检查是否已登录
    this.checkLoginStatus()
  },

  onShow() {
    // 页面显示时检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    if (app.globalData.isLogin) {
      // 已登录，跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 微信授权登录
  handleWechatLogin() {
    if (this.data.isLoading) return
    
    this.setData({ isLoading: true })
    
    wx.showLoading({
      title: '登录中...',
      mask: true
    })
    
    authUtils.login()
      .then((userInfo) => {
        console.log('登录成功:', userInfo)
        
        // 更新全局状态
        app.globalData.isLogin = true
        app.globalData.userInfo = userInfo
        
        wx.hideLoading()
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        })
        
        // 延迟跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
      })
      .catch((error) => {
        console.error('登录失败:', error)
        
        wx.hideLoading()
        wx.showToast({
          title: error.message || '登录失败',
          icon: 'none',
          duration: 2000
        })
      })
      .finally(() => {
        this.setData({ isLoading: false })
      })
  },

  // 获取用户信息
  handleGetUserProfile() {
    if (this.data.isLoading) return
    
    this.setData({ isLoading: true })
    
    authUtils.getUserProfile()
      .then((userInfo) => {
        console.log('获取用户信息成功:', userInfo)
        
        // 更新全局状态
        app.globalData.userInfo = userInfo
        
        wx.showToast({
          title: '授权成功',
          icon: 'success'
        })
      })
      .catch((error) => {
        console.error('获取用户信息失败:', error)
        
        wx.showToast({
          title: error.message || '授权失败',
          icon: 'none'
        })
      })
      .finally(() => {
        this.setData({ isLoading: false })
      })
  },

  // 获取手机号
  handleGetPhoneNumber(e) {
    if (this.data.isLoading) return
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      this.setData({ isLoading: true })
      
      authUtils.getPhoneNumber(e)
        .then((phoneNumber) => {
          console.log('获取手机号成功:', phoneNumber)
          
          // 更新本地用户信息
          const userInfo = app.globalData.userInfo
          if (userInfo) {
            userInfo.phoneNumber = phoneNumber
            app.globalData.userInfo = userInfo
            storageUtils.setUserInfo(userInfo)
          }
          
          wx.showToast({
            title: '绑定成功',
            icon: 'success'
          })
        })
        .catch((error) => {
          console.error('获取手机号失败:', error)
          
          wx.showToast({
            title: error.message || '获取手机号失败',
            icon: 'none'
          })
        })
        .finally(() => {
          this.setData({ isLoading: false })
        })
    } else {
      wx.showToast({
        title: '取消授权',
        icon: 'none'
      })
    }
  },

  // 跳过登录
  handleSkipLogin() {
    wx.showModal({
      title: '提示',
      content: '跳过登录将无法使用收藏、历史记录等功能，确定要跳过吗？',
      confirmText: '确定跳过',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 跳转到首页
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  // 查看用户协议
  handleViewUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 查看隐私政策
  handleViewPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 联系客服
  handleContactService() {
    wx.showModal({
      title: '联系客服',
      content: '如有问题，请联系客服微信：cloudsearch_service',
      confirmText: '复制微信号',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'cloudsearch_service',
            success: () => {
              wx.showToast({
                title: '已复制到剪贴板',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  }
})
