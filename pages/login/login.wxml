<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="login-bg">
    <view class="bg-circle bg-circle-1"></view>
    <view class="bg-circle bg-circle-2"></view>
    <view class="bg-circle bg-circle-3"></view>
  </view>

  <!-- 登录内容 -->
  <view class="login-content">
    <!-- Logo和标题 -->
    <view class="login-header">
      <image class="app-logo" src="/assets/images/logo.png" mode="aspectFit"></image>
      <text class="app-name">云搜罗</text>
      <text class="app-desc">发现云端资源，搜罗精彩内容</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 微信登录按钮 -->
      <button
        class="login-btn login-btn-primary"
        open-type="getUserInfo"
        bindgetuserinfo="handleWechatLogin"
        disabled="{{isLoading}}"
        loading="{{isLoading}}"
      >
        <text class="btn-icon" wx:if="{{!isLoading}}">🔐</text>
        <text class="btn-text">{{isLoading ? '登录中...' : '微信快速登录'}}</text>
      </button>

      <!-- 获取用户信息按钮 -->
      <button
        class="login-btn login-btn-secondary"
        wx:if="{{canIUseGetUserProfile}}"
        bindtap="handleGetUserProfile"
        disabled="{{isLoading}}"
      >
        <text class="btn-icon">👤</text>
        <text class="btn-text">获取用户信息</text>
      </button>

      <!-- 获取手机号按钮 -->
      <button
        class="login-btn login-btn-secondary"
        wx:if="{{canIUsePhoneNumber}}"
        open-type="getPhoneNumber"
        bindgetphonenumber="handleGetPhoneNumber"
        disabled="{{isLoading}}"
      >
        <text class="btn-icon">📱</text>
        <text class="btn-text">绑定手机号</text>
      </button>

      <!-- 跳过登录 -->
      <view class="skip-login" bindtap="handleSkipLogin">
        <text class="skip-text">暂不登录，先看看</text>
        <text class="skip-icon">→</text>
      </view>
    </view>

    <!-- 登录说明 -->
    <view class="login-tips">
      <text class="tips-text">登录即表示同意</text>
      <text class="tips-link" bindtap="handleViewUserAgreement">《用户协议》</text>
      <text class="tips-text">和</text>
      <text class="tips-link" bindtap="handleViewPrivacyPolicy">《隐私政策》</text>
    </view>

    <!-- 底部信息 -->
    <view class="login-footer">
      <view class="footer-item" bindtap="handleContactService">
        <i-icon type="service" size="16" color="#999"></i-icon>
        <text class="footer-text">联系客服</text>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">v1.0.0</text>
  </view>
</view>
