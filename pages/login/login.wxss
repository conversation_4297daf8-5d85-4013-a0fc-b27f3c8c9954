/* pages/login/login.wxss */
.login-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.bg-circle-3 {
  width: 100rpx;
  height: 100rpx;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 登录内容 */
.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 登录表单 */
.login-form {
  margin-bottom: 40rpx;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn::after {
  border: none;
}

.login-btn-primary {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);
}

.login-btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.login-btn-secondary {
  background: #fff;
  color: #1890ff;
  border: 2rpx solid #1890ff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.login-btn-secondary:active {
  background: #f0f8ff;
  transform: translateY(2rpx);
}

.login-btn[disabled] {
  opacity: 0.6;
  transform: none !important;
}

.btn-text {
  margin-left: 12rpx;
}

/* 跳过登录 */
.skip-login {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  margin-top: 20rpx;
}

.skip-text {
  font-size: 28rpx;
  color: #999;
  margin-right: 8rpx;
}

/* 登录说明 */
.login-tips {
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
}

.tips-link {
  font-size: 24rpx;
  color: #1890ff;
  text-decoration: underline;
}

/* 底部信息 */
.login-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.footer-item:active {
  background: #e8e8e8;
}

.footer-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

/* 版本信息 */
.version-info {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.version-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 响应式适配 */
@media (max-height: 800px) {
  .login-container {
    padding: 20rpx;
  }
  
  .login-content {
    padding: 40rpx 30rpx;
  }
  
  .login-header {
    margin-bottom: 40rpx;
  }
  
  .app-logo {
    width: 100rpx;
    height: 100rpx;
  }
  
  .app-name {
    font-size: 40rpx;
  }
}
