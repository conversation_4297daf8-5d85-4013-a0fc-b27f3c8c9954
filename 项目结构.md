# 云盘搜索小程序项目结构

## 目录结构

```
cloud-search/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── project.config.json   # 项目配置文件
├── sitemap.json          # 站点地图配置
├── README.md             # 项目说明文档
├── 需求文档.md           # 需求文档
├── 项目结构.md           # 项目结构说明
│
├── components/           # 自定义组件
│   ├── search-bar/      # 搜索栏组件
│   ├── resource-item/   # 资源列表项组件
│   ├── loading/         # 加载组件
│   └── empty/           # 空状态组件
│
├── pages/               # 页面文件
│   ├── login/          # 登录页
│   │   ├── login.js
│   │   ├── login.wxml
│   │   ├── login.wxss
│   │   └── login.json
│   │
│   ├── index/          # 首页
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   │
│   ├── search/         # 搜索结果页
│   │   ├── search.js
│   │   ├── search.wxml
│   │   ├── search.wxss
│   │   └── search.json
│   │
│   ├── detail/         # 资源详情页
│   │   ├── detail.js
│   │   ├── detail.wxml
│   │   ├── detail.wxss
│   │   └── detail.json
│   │
│   ├── profile/        # 我的页面
│   │   ├── profile.js
│   │   ├── profile.wxml
│   │   ├── profile.wxss
│   │   └── profile.json
│   │
│   └── settings/       # 设置页面
│       ├── settings.js
│       ├── settings.wxml
│       ├── settings.wxss
│       └── settings.json
│
├── utils/              # 工具函数
│   ├── request.js      # 网络请求封装
│   ├── storage.js      # 本地存储封装
│   ├── auth.js         # 认证相关工具
│   ├── format.js       # 格式化工具
│   └── validate.js     # 验证工具
│
├── api/                # API接口
│   ├── user.js         # 用户相关接口
│   ├── search.js       # 搜索相关接口
│   ├── resource.js     # 资源相关接口
│   └── common.js       # 通用接口
│
├── config/             # 配置文件
│   ├── api.js          # API配置
│   ├── constants.js    # 常量配置
│   └── env.js          # 环境配置
│
├── styles/             # 全局样式
│   ├── variables.wxss  # 样式变量
│   ├── common.wxss     # 通用样式
│   └── mixins.wxss     # 样式混入
│
├── assets/             # 静态资源
│   ├── images/         # 图片资源
│   ├── icons/          # 图标资源
│   └── fonts/          # 字体资源
│
└── docs/               # 文档
    ├── api.md          # API文档
    ├── components.md   # 组件文档
    └── deployment.md   # 部署文档
```

## 文件说明

### 根目录文件

#### app.js
小程序入口文件，包含：
- 全局生命周期函数
- 全局数据
- 全局方法

#### app.json
小程序配置文件，包含：
- 页面路径配置
- 窗口配置
- 底部导航配置
- 网络超时配置
- 权限配置

#### app.wxss
全局样式文件，包含：
- 全局样式重置
- 通用样式类
- 主题色彩定义

### 页面结构

每个页面包含四个文件：
- `.js` - 页面逻辑
- `.wxml` - 页面结构
- `.wxss` - 页面样式
- `.json` - 页面配置

### 组件结构

每个组件包含四个文件：
- `.js` - 组件逻辑
- `.wxml` - 组件结构
- `.wxss` - 组件样式
- `.json` - 组件配置

## 命名规范

### 文件命名
- 使用小写字母
- 单词间用连字符分隔
- 页面文件夹使用功能名称

### 变量命名
- 使用驼峰命名法
- 常量使用大写字母和下划线

### 样式类命名
- 使用BEM命名规范
- 组件样式使用组件名前缀

## 代码组织

### 页面结构
```javascript
// pages/example/example.js
Page({
  data: {
    // 页面数据
  },
  
  onLoad(options) {
    // 页面加载
  },
  
  onShow() {
    // 页面显示
  },
  
  // 自定义方法
  handleEvent() {
    // 事件处理
  }
})
```

### 组件结构
```javascript
// components/example/example.js
Component({
  properties: {
    // 组件属性
  },
  
  data: {
    // 组件数据
  },
  
  methods: {
    // 组件方法
  }
})
```

### API接口组织
```javascript
// api/user.js
const request = require('../utils/request')

module.exports = {
  // 用户登录
  login(data) {
    return request.post('/user/login', data)
  },
  
  // 获取用户信息
  getUserInfo() {
    return request.get('/user/info')
  }
}
```

## 开发规范

### 1. 代码风格
- 使用ES6+语法
- 统一缩进（2个空格）
- 使用分号结尾
- 注释规范

### 2. 组件开发
- 组件功能单一
- 组件可复用
- 组件接口清晰

### 3. 数据处理
- 统一数据格式
- 数据验证
- 错误处理

### 4. 样式管理
- 使用iView Weapp组件
- 自定义样式遵循设计规范
- 响应式设计

## 部署配置

### 开发环境
- 本地开发服务器
- 热重载
- 调试工具

### 生产环境
- 代码压缩
- 图片优化
- 性能优化

### 版本管理
- Git版本控制
- 分支管理
- 发布流程 