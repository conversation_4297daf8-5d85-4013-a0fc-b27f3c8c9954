/* 通用样式类 */

/* 布局相关 */
.layout-flex {
  display: flex;
}

.layout-flex-column {
  display: flex;
  flex-direction: column;
}

.layout-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.layout-flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.layout-flex-start {
  display: flex;
  align-items: flex-start;
}

.layout-flex-end {
  display: flex;
  align-items: flex-end;
}

.layout-flex-1 {
  flex: 1;
}

.layout-flex-wrap {
  flex-wrap: wrap;
}

/* 容器相关 */
.container-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.container-content {
  padding: var(--spacing-md);
}

.container-section {
  background-color: var(--bg-primary);
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-medium);
}

/* 卡片相关 */
.card-container {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-light);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1rpx solid var(--border-light);
  background-color: var(--bg-secondary);
}

/* 列表相关 */
.list-container {
  background-color: var(--bg-primary);
}

.list-item {
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color var(--transition-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: var(--bg-secondary);
}

.list-item-content {
  display: flex;
  align-items: center;
}

.list-item-icon {
  margin-right: var(--spacing-md);
}

.list-item-text {
  flex: 1;
}

.list-item-arrow {
  color: var(--text-muted);
}

/* 按钮相关 */
.btn-container {
  padding: var(--spacing-md);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-md);
  transition: all var(--transition-base);
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-md);
  transition: all var(--transition-base);
}

.btn-secondary:active {
  background-color: var(--border-light);
}

.btn-ghost {
  background-color: transparent;
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-md);
  transition: all var(--transition-base);
}

.btn-ghost:active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-large {
  padding: var(--spacing-xl) var(--spacing-xxl);
  font-size: var(--font-size-lg);
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 表单相关 */
.form-container {
  padding: var(--spacing-lg);
}

.form-item {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.form-input {
  width: 100%;
  padding: var(--spacing-lg);
  border: 1rpx solid var(--border-color);
  border-radius: var(--border-radius-medium);
  font-size: var(--font-size-md);
  background-color: var(--bg-primary);
  transition: border-color var(--transition-base);
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-input-error {
  border-color: var(--error-color);
}

.form-error-text {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

/* 头像相关 */
.avatar-container {
  position: relative;
  display: inline-block;
}

.avatar-image {
  border-radius: var(--border-radius-round);
  overflow: hidden;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar-medium {
  width: 80rpx;
  height: 80rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

/* 标签相关 */
.tag-container {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-sm);
  line-height: 1;
}

.tag-primary {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.tag-success {
  background-color: #f6ffed;
  color: var(--success-color);
}

.tag-warning {
  background-color: #fff7e6;
  color: var(--warning-color);
}

.tag-error {
  background-color: #fff2f0;
  color: var(--error-color);
}

/* 分割线相关 */
.divider {
  height: 1rpx;
  background-color: var(--border-light);
  margin: var(--spacing-lg) 0;
}

.divider-text {
  position: relative;
  text-align: center;
  margin: var(--spacing-lg) 0;
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: var(--border-light);
}

.divider-text text {
  background-color: var(--bg-primary);
  padding: 0 var(--spacing-md);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* 加载相关 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
}

.loading-text {
  margin-left: var(--spacing-md);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* 空状态相关 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.3;
}

.empty-text {
  color: var(--text-muted);
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-lg);
}

/* 固定底部 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
  padding: var(--spacing-lg);
  z-index: var(--z-index-fixed);
}
