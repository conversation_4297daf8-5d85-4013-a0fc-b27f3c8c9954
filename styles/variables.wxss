/* 样式变量定义 */

/* 主题色彩 */
:root {
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  
  --secondary-color: #f5f5f5;
  --secondary-light: #fafafa;
  --secondary-dark: #d9d9d9;
  
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;
  
  /* 文本颜色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-muted: #8c8c8c;
  --text-disabled: #bfbfbf;
  --text-white: #ffffff;
  
  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-disabled: #f5f5f5;
  
  /* 边框颜色 */
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  --border-dark: #bfbfbf;
  
  /* 阴影 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --border-radius-small: 4rpx;
  --border-radius-medium: 8rpx;
  --border-radius-large: 16rpx;
  --border-radius-round: 50%;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  --spacing-xxl: 48rpx;
  
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;
  --font-size-title: 48rpx;
  
  /* 行高 */
  --line-height-sm: 1.2;
  --line-height-md: 1.4;
  --line-height-lg: 1.6;
  
  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  
  /* 层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  
  /* 动画时间 */
  --transition-fast: 0.15s;
  --transition-base: 0.3s;
  --transition-slow: 0.5s;
  
  /* 动画函数 */
  --ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
  --ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
}
