# 云盘搜索小程序

基于微信小程序平台的网盘资源搜索应用，支持搜索夸克网盘和阿里云盘的分享资源。

## 功能特性

- 🔍 **智能搜索**: 支持关键词搜索夸克网盘和阿里云盘资源
- 👤 **用户管理**: 微信授权登录，个人信息管理
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **现代UI**: 基于iView Weapp组件库
- ⚡ **高性能**: 优化的搜索和列表展示

## 技术栈

- **框架**: 微信小程序原生开发
- **UI库**: [iView Weapp](https://github.com/TalkingData/iview-weapp)
- **样式**: WXSS
- **状态管理**: 小程序原生数据绑定

## 项目结构

```
cloud-search/
├── components/          # 自定义组件
├── pages/              # 页面文件
├── utils/              # 工具函数
├── api/                # API接口
├── config/             # 配置文件
├── styles/             # 全局样式
├── assets/             # 静态资源
└── docs/               # 文档
```

详细的项目结构请查看 [项目结构.md](./项目结构.md)

## 快速开始

### 环境要求

- 微信开发者工具
- Node.js (可选，用于构建工具)

### 安装依赖

1. 克隆项目
```bash
git clone [项目地址]
cd cloud-search
```

2. 安装iView Weapp
```bash
# 在项目根目录下执行
npm install @talkingdata/iview-weapp
```

3. 在微信开发者工具中导入项目

### 开发配置

1. 在微信开发者工具中打开项目
2. 配置AppID（在 `project.config.json` 中）
3. 开启ES6转ES5功能
4. 开启增强编译

### 运行项目

1. 在微信开发者工具中点击"编译"
2. 在模拟器中预览效果
3. 真机调试（可选）

## 页面说明

### 主要页面

- **登录页** (`pages/login/`): 微信授权登录
- **首页** (`pages/index/`): 搜索功能和资源列表
- **搜索结果页** (`pages/search/`): 搜索结果展示
- **资源详情页** (`pages/detail/`): 资源详细信息
- **我的页面** (`pages/profile/`): 用户信息和功能入口
- **设置页** (`pages/settings/`): 个人信息编辑

## 开发指南

### 添加新页面

1. 在 `pages/` 目录下创建新文件夹
2. 创建四个文件：`.js`、`.wxml`、`.wxss`、`.json`
3. 在 `app.json` 中注册页面路径

### 添加新组件

1. 在 `components/` 目录下创建新文件夹
2. 创建四个文件：`.js`、`.wxml`、`.wxss`、`.json`
3. 在页面中引入组件

### API接口开发

1. 在 `api/` 目录下创建接口文件
2. 使用 `utils/request.js` 进行网络请求
3. 在页面中调用接口

## 配置说明

### app.json 配置

```json
{
  "pages": [
    "pages/login/login",
    "pages/index/index",
    "pages/search/search",
    "pages/detail/detail",
    "pages/profile/profile",
    "pages/settings/settings"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "云盘搜索",
    "navigationBarTextStyle": "black"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的"
      }
    ]
  }
}
```

### 环境配置

在 `config/env.js` 中配置不同环境的API地址：

```javascript
const env = {
  development: {
    baseUrl: 'http://localhost:3000/api'
  },
  production: {
    baseUrl: 'https://your-api-domain.com/api'
  }
}

module.exports = env
```

## 部署说明

### 开发环境

1. 在微信开发者工具中测试
2. 使用真机调试功能
3. 检查控制台错误信息

### 生产环境

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 提交审核
4. 审核通过后发布

## 常见问题

### Q: 如何修改小程序名称？
A: 在 `app.json` 中修改 `navigationBarTitleText` 字段。

### Q: 如何添加新的网盘类型？
A: 在搜索相关代码中添加新的网盘类型判断逻辑。

### Q: 如何自定义主题色彩？
A: 在 `styles/variables.wxss` 中修改CSS变量。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目地址: [项目GitHub地址]

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础搜索功能
- 用户登录功能
- 资源列表展示

---

更多详细信息请查看 [需求文档.md](./需求文档.md) 