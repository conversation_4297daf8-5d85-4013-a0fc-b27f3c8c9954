# 图标资源目录

此目录用于存放应用中使用的图标资源。

## 🚨 重要提示

当前项目中的 tabBar 图标已临时移除，避免编译错误。如需添加图标，请按以下步骤操作：

### 1. 准备图标文件
需要准备以下图标文件：
- `home.png` - 首页图标 (81x81px)
- `home-active.png` - 首页激活图标 (81x81px)
- `profile.png` - 我的图标 (81x81px)
- `profile-active.png` - 我的激活图标 (81x81px)

### 2. 图标规范
- **格式**: PNG格式，支持透明背景
- **尺寸**: 81x81px（微信小程序tabBar图标标准尺寸）
- **大小**: 建议不超过40KB
- **设计**: 简洁明了，符合小程序设计规范

### 3. 添加图标后的配置
将图标文件放入此目录后，需要修改 `app.json` 文件中的 tabBar 配置：

```json
"tabBar": {
  "color": "#999999",
  "selectedColor": "#1890ff",
  "backgroundColor": "#ffffff",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/index/index",
      "text": "首页",
      "iconPath": "assets/icons/home.png",
      "selectedIconPath": "assets/icons/home-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的",
      "iconPath": "assets/icons/profile.png",
      "selectedIconPath": "assets/icons/profile-active.png"
    }
  ]
}
```

### 4. 图标设计建议
- **首页图标**: 可使用房子、主页等图标
- **我的图标**: 可使用用户头像、个人等图标
- **激活状态**: 通常使用填充版本或不同颜色
- **风格统一**: 保持图标风格一致

### 5. 在线图标资源
可以从以下网站获取图标：
- [Iconfont](https://www.iconfont.cn/) - 阿里巴巴图标库
- [IconPark](https://iconpark.oceanengine.com/) - 字节跳动图标库
- [Feather Icons](https://feathericons.com/) - 简洁线性图标
- [Heroicons](https://heroicons.com/) - 现代图标集

## 其他功能图标

除了 tabBar 图标，还可以添加以下功能图标：

### 搜索相关
- search.png - 搜索图标
- filter.png - 筛选图标
- sort.png - 排序图标
- clear.png - 清除图标

### 操作相关
- favorite.png - 收藏图标
- share.png - 分享图标
- download.png - 下载图标
- copy.png - 复制图标

### 网盘类型
- quark.png - 夸克网盘图标
- aliyun.png - 阿里云盘图标
- baidu.png - 百度网盘图标

### 状态图标
- loading.png - 加载图标
- success.png - 成功图标
- error.png - 错误图标
- warning.png - 警告图标
