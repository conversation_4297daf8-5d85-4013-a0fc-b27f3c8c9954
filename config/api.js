// API配置文件

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
}

// 当前环境
const CURRENT_ENV = ENV.DEVELOPMENT

// API基础配置
const API_CONFIG = {
  [ENV.DEVELOPMENT]: {
    baseUrl: 'https://dev-api.cloudsearch.com',
    timeout: 10000,
    enableMock: true
  },
  [ENV.TEST]: {
    baseUrl: 'https://test-api.cloudsearch.com',
    timeout: 10000,
    enableMock: false
  },
  [ENV.PRODUCTION]: {
    baseUrl: 'https://api.cloudsearch.com',
    timeout: 15000,
    enableMock: false
  }
}

// 获取当前环境配置
function getCurrentConfig() {
  return API_CONFIG[CURRENT_ENV]
}

// API端点配置
const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh',
    GET_USER_INFO: '/auth/userinfo',
    BIND_PHONE: '/auth/bind-phone',
    SEND_CODE: '/auth/send-code',
    VERIFY_CODE: '/auth/verify-code'
  },
  
  // 用户相关
  USER: {
    INFO: '/user/info',
    UPDATE: '/user/info',
    AVATAR: '/user/avatar',
    PASSWORD: '/user/password',
    SETTINGS: '/user/settings',
    STATS: '/user/stats',
    FAVORITES: '/user/favorites',
    SEARCH_HISTORY: '/user/search-history',
    FEEDBACK: '/user/feedback',
    CHECK_IN: '/user/check-in',
    POINTS: '/user/points',
    DELETE_ACCOUNT: '/user/delete-account'
  },
  
  // 搜索相关
  SEARCH: {
    RESOURCES: '/search',
    HOT_KEYWORDS: '/search/hot-keywords',
    SUGGESTIONS: '/search/suggestions',
    ADVANCED: '/search/advanced',
    CATEGORY: '/search/category',
    DISK_TYPE: '/search/disk-type',
    FILTERS: '/search/filters',
    USERS: '/search/users',
    RELATED: '/search/related',
    STATS: '/search/stats',
    TRENDS: '/search/trends',
    SAVE: '/search/save',
    SAVED: '/search/saved',
    AUTOCOMPLETE: '/search/autocomplete',
    HISTORY_STATS: '/search/history-stats',
    REPORT: '/search/report',
    RANKING: '/search/ranking',
    SMART: '/search/smart',
    VOICE: '/search/voice',
    IMAGE: '/search/image',
    CONFIG: '/search/config'
  },
  
  // 资源相关
  RESOURCE: {
    DETAIL: '/resource/:id',
    LIST: '/resource/list',
    RECOMMENDED: '/resource/recommended',
    HOT: '/resource/hot',
    LATEST: '/resource/latest',
    RELATED: '/resource/:id/related',
    CATEGORIES: '/resource/categories',
    CATEGORY: '/resource/category/:categoryId',
    TAGS: '/resource/tags',
    TAG: '/resource/tag',
    FAVORITE: '/resource/:id/favorite',
    SHARE: '/resource/:id/share',
    REPORT: '/resource/:id/report',
    RATE: '/resource/:id/rate',
    RATINGS: '/resource/:id/ratings',
    COMMENTS: '/resource/:id/comments',
    LIKE: '/resource/:id/like',
    VIEW: '/resource/:id/view',
    STATS: '/resource/:id/stats',
    HISTORY: '/resource/:id/history',
    DOWNLOAD: '/resource/:id/download',
    DOWNLOAD_LINK: '/resource/:id/download-link',
    VALIDATE: '/resource/:id/validate',
    PREVIEW: '/resource/:id/preview',
    SUBMIT: '/resource/submit'
  },
  
  // 通用接口
  COMMON: {
    CONFIG: '/common/config',
    VERSION: '/common/version',
    CHECK_UPDATE: '/common/check-update',
    NOTICES: '/common/notices',
    BANNERS: '/common/banners',
    HELP: '/common/help',
    FAQ: '/common/faq',
    CONTACT: '/common/contact',
    FEEDBACK: '/common/feedback',
    UPLOAD: '/common/upload',
    UPLOAD_IMAGE: '/common/upload/image',
    UPLOAD_TOKEN: '/common/upload/token',
    REGIONS: '/common/regions',
    CITIES: '/common/cities',
    DISTRICTS: '/common/districts',
    TIME: '/common/time',
    STATUS: '/common/status',
    STATISTICS: '/common/statistics',
    BEHAVIOR: '/common/behavior',
    IP_INFO: '/common/ip-info',
    WEATHER: '/common/weather',
    SHORT_URL: '/common/short-url',
    QRCODE: '/common/qrcode',
    EMAIL: '/common/email',
    SMS: '/common/sms',
    CAPTCHA: '/common/captcha',
    SENSITIVE_WORDS: '/common/sensitive-words',
    AUDIT: '/common/audit',
    SHARE: '/common/share',
    PROMOTION: '/common/promotion'
  }
}

// 请求方法
const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
}

// 响应状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
}

// 业务状态码
const BUSINESS_CODE = {
  SUCCESS: 0,
  FAIL: -1,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500
}

// 请求头配置
const REQUEST_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-Requested-With': 'XMLHttpRequest'
}

// 超时配置
const TIMEOUT_CONFIG = {
  REQUEST: 10000,
  UPLOAD: 30000,
  DOWNLOAD: 60000
}

// 重试配置
const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  RETRY_METHODS: ['GET'],
  RETRY_STATUS_CODES: [408, 429, 500, 502, 503, 504]
}

// 缓存配置
const CACHE_CONFIG = {
  ENABLE: true,
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  MAX_SIZE: 100,
  CACHE_METHODS: ['GET']
}

// Mock数据配置
const MOCK_CONFIG = {
  ENABLE: CURRENT_ENV === ENV.DEVELOPMENT,
  DELAY: 500,
  SUCCESS_RATE: 0.9
}

// 日志配置
const LOG_CONFIG = {
  ENABLE: true,
  LEVEL: CURRENT_ENV === ENV.PRODUCTION ? 'error' : 'debug',
  MAX_LOGS: 1000
}

// 安全配置
const SECURITY_CONFIG = {
  ENABLE_CSRF: true,
  ENABLE_XSS_PROTECTION: true,
  ENABLE_CONTENT_TYPE_VALIDATION: true,
  ALLOWED_ORIGINS: [
    'https://cloudsearch.com',
    'https://www.cloudsearch.com'
  ]
}

// 限流配置
const RATE_LIMIT_CONFIG = {
  ENABLE: true,
  MAX_REQUESTS: 100,
  WINDOW_SIZE: 60 * 1000, // 1分钟
  BLOCK_DURATION: 5 * 60 * 1000 // 5分钟
}

// 导出配置
module.exports = {
  ENV,
  CURRENT_ENV,
  API_CONFIG,
  getCurrentConfig,
  API_ENDPOINTS,
  HTTP_METHODS,
  HTTP_STATUS,
  BUSINESS_CODE,
  REQUEST_HEADERS,
  TIMEOUT_CONFIG,
  RETRY_CONFIG,
  CACHE_CONFIG,
  MOCK_CONFIG,
  LOG_CONFIG,
  SECURITY_CONFIG,
  RATE_LIMIT_CONFIG
}
