// 环境配置文件

// 环境类型
const ENV_TYPES = {
  DEVELOPMENT: 'development',
  TEST: 'test',
  STAGING: 'staging',
  PRODUCTION: 'production'
}

// 当前环境（根据实际情况修改）
const CURRENT_ENV = ENV_TYPES.DEVELOPMENT

// 开发环境配置
const DEVELOPMENT_CONFIG = {
  // API配置
  api: {
    baseUrl: 'https://dev-api.cloudsearch.com',
    timeout: 10000,
    enableMock: true,
    enableLog: true
  },
  
  // 微信小程序配置
  wechat: {
    appId: 'wx_dev_app_id',
    enableDebug: true
  },
  
  // 存储配置
  storage: {
    prefix: 'dev_cloudsearch_',
    enableEncrypt: false
  },
  
  // 日志配置
  log: {
    level: 'debug',
    enableConsole: true,
    enableRemote: false,
    maxLogs: 1000
  },
  
  // 缓存配置
  cache: {
    enable: true,
    defaultTTL: 5 * 60 * 1000, // 5分钟
    maxSize: 50
  },
  
  // 性能监控
  performance: {
    enable: true,
    sampleRate: 1.0
  },
  
  // 错误监控
  error: {
    enable: true,
    autoReport: false
  },
  
  // 统计分析
  analytics: {
    enable: false
  }
}

// 测试环境配置
const TEST_CONFIG = {
  // API配置
  api: {
    baseUrl: 'https://test-api.cloudsearch.com',
    timeout: 10000,
    enableMock: false,
    enableLog: true
  },
  
  // 微信小程序配置
  wechat: {
    appId: 'wx_test_app_id',
    enableDebug: true
  },
  
  // 存储配置
  storage: {
    prefix: 'test_cloudsearch_',
    enableEncrypt: false
  },
  
  // 日志配置
  log: {
    level: 'info',
    enableConsole: true,
    enableRemote: true,
    maxLogs: 500
  },
  
  // 缓存配置
  cache: {
    enable: true,
    defaultTTL: 10 * 60 * 1000, // 10分钟
    maxSize: 100
  },
  
  // 性能监控
  performance: {
    enable: true,
    sampleRate: 0.5
  },
  
  // 错误监控
  error: {
    enable: true,
    autoReport: true
  },
  
  // 统计分析
  analytics: {
    enable: true
  }
}

// 预发布环境配置
const STAGING_CONFIG = {
  // API配置
  api: {
    baseUrl: 'https://staging-api.cloudsearch.com',
    timeout: 15000,
    enableMock: false,
    enableLog: false
  },
  
  // 微信小程序配置
  wechat: {
    appId: 'wx_staging_app_id',
    enableDebug: false
  },
  
  // 存储配置
  storage: {
    prefix: 'staging_cloudsearch_',
    enableEncrypt: true
  },
  
  // 日志配置
  log: {
    level: 'warn',
    enableConsole: false,
    enableRemote: true,
    maxLogs: 200
  },
  
  // 缓存配置
  cache: {
    enable: true,
    defaultTTL: 15 * 60 * 1000, // 15分钟
    maxSize: 100
  },
  
  // 性能监控
  performance: {
    enable: true,
    sampleRate: 0.1
  },
  
  // 错误监控
  error: {
    enable: true,
    autoReport: true
  },
  
  // 统计分析
  analytics: {
    enable: true
  }
}

// 生产环境配置
const PRODUCTION_CONFIG = {
  // API配置
  api: {
    baseUrl: 'https://api.cloudsearch.com',
    timeout: 15000,
    enableMock: false,
    enableLog: false
  },
  
  // 微信小程序配置
  wechat: {
    appId: 'wx_prod_app_id',
    enableDebug: false
  },
  
  // 存储配置
  storage: {
    prefix: 'cloudsearch_',
    enableEncrypt: true
  },
  
  // 日志配置
  log: {
    level: 'error',
    enableConsole: false,
    enableRemote: true,
    maxLogs: 100
  },
  
  // 缓存配置
  cache: {
    enable: true,
    defaultTTL: 30 * 60 * 1000, // 30分钟
    maxSize: 200
  },
  
  // 性能监控
  performance: {
    enable: true,
    sampleRate: 0.01
  },
  
  // 错误监控
  error: {
    enable: true,
    autoReport: true
  },
  
  // 统计分析
  analytics: {
    enable: true
  }
}

// 环境配置映射
const ENV_CONFIG_MAP = {
  [ENV_TYPES.DEVELOPMENT]: DEVELOPMENT_CONFIG,
  [ENV_TYPES.TEST]: TEST_CONFIG,
  [ENV_TYPES.STAGING]: STAGING_CONFIG,
  [ENV_TYPES.PRODUCTION]: PRODUCTION_CONFIG
}

// 获取当前环境配置
function getCurrentConfig() {
  return ENV_CONFIG_MAP[CURRENT_ENV] || DEVELOPMENT_CONFIG
}

// 获取指定环境配置
function getConfig(env) {
  return ENV_CONFIG_MAP[env] || DEVELOPMENT_CONFIG
}

// 判断是否为开发环境
function isDevelopment() {
  return CURRENT_ENV === ENV_TYPES.DEVELOPMENT
}

// 判断是否为测试环境
function isTest() {
  return CURRENT_ENV === ENV_TYPES.TEST
}

// 判断是否为预发布环境
function isStaging() {
  return CURRENT_ENV === ENV_TYPES.STAGING
}

// 判断是否为生产环境
function isProduction() {
  return CURRENT_ENV === ENV_TYPES.PRODUCTION
}

// 获取API基础URL
function getApiBaseUrl() {
  return getCurrentConfig().api.baseUrl
}

// 获取微信小程序AppId
function getWechatAppId() {
  return getCurrentConfig().wechat.appId
}

// 获取存储前缀
function getStoragePrefix() {
  return getCurrentConfig().storage.prefix
}

// 是否启用调试模式
function isDebugEnabled() {
  return getCurrentConfig().wechat.enableDebug
}

// 是否启用Mock数据
function isMockEnabled() {
  return getCurrentConfig().api.enableMock
}

// 是否启用日志
function isLogEnabled() {
  return getCurrentConfig().log.enableConsole
}

// 是否启用远程日志
function isRemoteLogEnabled() {
  return getCurrentConfig().log.enableRemote
}

// 是否启用缓存
function isCacheEnabled() {
  return getCurrentConfig().cache.enable
}

// 是否启用性能监控
function isPerformanceEnabled() {
  return getCurrentConfig().performance.enable
}

// 是否启用错误监控
function isErrorMonitorEnabled() {
  return getCurrentConfig().error.enable
}

// 是否启用统计分析
function isAnalyticsEnabled() {
  return getCurrentConfig().analytics.enable
}

// 获取日志级别
function getLogLevel() {
  return getCurrentConfig().log.level
}

// 获取性能监控采样率
function getPerformanceSampleRate() {
  return getCurrentConfig().performance.sampleRate
}

module.exports = {
  ENV_TYPES,
  CURRENT_ENV,
  getCurrentConfig,
  getConfig,
  isDevelopment,
  isTest,
  isStaging,
  isProduction,
  getApiBaseUrl,
  getWechatAppId,
  getStoragePrefix,
  isDebugEnabled,
  isMockEnabled,
  isLogEnabled,
  isRemoteLogEnabled,
  isCacheEnabled,
  isPerformanceEnabled,
  isErrorMonitorEnabled,
  isAnalyticsEnabled,
  getLogLevel,
  getPerformanceSampleRate
}
