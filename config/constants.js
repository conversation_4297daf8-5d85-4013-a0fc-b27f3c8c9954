// 常量配置文件

// 应用信息
const APP_INFO = {
  NAME: '云搜罗',
  VERSION: '1.0.0',
  DESCRIPTION: '云盘资源搜索小程序',
  AUTHOR: '云搜罗团队',
  COPYRIGHT: '© 2024 云搜罗. All rights reserved.'
}

// 网盘类型
const DISK_TYPES = {
  QUARK: 'quark',
  ALIYUN: 'aliyun',
  BAIDU: 'baidu',
  ONEDRIVE: 'onedrive',
  GOOGLEDRIVE: 'googledrive',
  LANZOU: 'lanzou',
  '115': '115'
}

// 网盘类型显示名称
const DISK_TYPE_NAMES = {
  [DISK_TYPES.QUARK]: '夸克网盘',
  [DISK_TYPES.ALIYUN]: '阿里云盘',
  [DISK_TYPES.BAIDU]: '百度网盘',
  [DISK_TYPES.ONEDRIVE]: 'OneDrive',
  [DISK_TYPES.GOOGLEDRIVE]: 'Google Drive',
  [DISK_TYPES.LANZOU]: '蓝奏云',
  [DISK_TYPES['115']]: '115网盘'
}

// 资源分类
const RESOURCE_CATEGORIES = {
  VIDEO: 'video',
  AUDIO: 'audio',
  IMAGE: 'image',
  DOCUMENT: 'document',
  SOFTWARE: 'software',
  GAME: 'game',
  EBOOK: 'ebook',
  OTHER: 'other'
}

// 资源分类显示名称
const RESOURCE_CATEGORY_NAMES = {
  [RESOURCE_CATEGORIES.VIDEO]: '视频',
  [RESOURCE_CATEGORIES.AUDIO]: '音频',
  [RESOURCE_CATEGORIES.IMAGE]: '图片',
  [RESOURCE_CATEGORIES.DOCUMENT]: '文档',
  [RESOURCE_CATEGORIES.SOFTWARE]: '软件',
  [RESOURCE_CATEGORIES.GAME]: '游戏',
  [RESOURCE_CATEGORIES.EBOOK]: '电子书',
  [RESOURCE_CATEGORIES.OTHER]: '其他'
}

// 排序方式
const SORT_TYPES = {
  RELEVANCE: 'relevance',
  TIME_DESC: 'time_desc',
  TIME_ASC: 'time_asc',
  SIZE_DESC: 'size_desc',
  SIZE_ASC: 'size_asc',
  VIEW_DESC: 'view_desc',
  FAVORITE_DESC: 'favorite_desc'
}

// 排序方式显示名称
const SORT_TYPE_NAMES = {
  [SORT_TYPES.RELEVANCE]: '相关度',
  [SORT_TYPES.TIME_DESC]: '最新',
  [SORT_TYPES.TIME_ASC]: '最早',
  [SORT_TYPES.SIZE_DESC]: '大小降序',
  [SORT_TYPES.SIZE_ASC]: '大小升序',
  [SORT_TYPES.VIEW_DESC]: '热度',
  [SORT_TYPES.FAVORITE_DESC]: '收藏数'
}

// 用户状态
const USER_STATUS = {
  NORMAL: 'normal',
  BANNED: 'banned',
  PENDING: 'pending',
  DELETED: 'deleted'
}

// 用户角色
const USER_ROLES = {
  USER: 'user',
  VIP: 'vip',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin'
}

// 性别
const GENDER = {
  UNKNOWN: 0,
  MALE: 1,
  FEMALE: 2
}

// 性别显示名称
const GENDER_NAMES = {
  [GENDER.UNKNOWN]: '未知',
  [GENDER.MALE]: '男',
  [GENDER.FEMALE]: '女'
}

// 页面路径
const PAGE_PATHS = {
  LOGIN: '/pages/login/login',
  INDEX: '/pages/index/index',
  SEARCH: '/pages/search/search',
  DETAIL: '/pages/detail/detail',
  PROFILE: '/pages/profile/profile',
  SETTINGS: '/pages/settings/settings'
}

// 存储键名
const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  SEARCH_HISTORY: 'searchHistory',
  FAVORITES: 'favorites',
  SETTINGS: 'settings',
  CACHE: 'cache'
}

// 事件名称
const EVENT_NAMES = {
  LOGIN_SUCCESS: 'loginSuccess',
  LOGOUT: 'logout',
  USER_INFO_UPDATE: 'userInfoUpdate',
  SEARCH: 'search',
  FAVORITE_CHANGE: 'favoriteChange',
  THEME_CHANGE: 'themeChange'
}

// 错误码
const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 错误信息
const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_CODES.AUTH_ERROR]: '登录已过期，请重新登录',
  [ERROR_CODES.PERMISSION_ERROR]: '权限不足，无法访问',
  [ERROR_CODES.VALIDATION_ERROR]: '数据验证失败',
  [ERROR_CODES.SERVER_ERROR]: '服务器错误，请稍后重试',
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误，请稍后重试'
}

// 文件大小单位
const FILE_SIZE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']

// 时间格式
const TIME_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATETIME_SHORT: 'MM-DD HH:mm'
}

// 分页配置
const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_PAGE: 1
}

// 搜索配置
const SEARCH_CONFIG = {
  MIN_KEYWORD_LENGTH: 1,
  MAX_KEYWORD_LENGTH: 100,
  MAX_HISTORY_COUNT: 20,
  SUGGESTION_COUNT: 10,
  HOT_KEYWORD_COUNT: 10
}

// 上传配置
const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  ALLOWED_VIDEO_TYPES: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
  ALLOWED_AUDIO_TYPES: ['mp3', 'wav', 'flac', 'aac'],
  ALLOWED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
}

// 缓存配置
const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  MAX_SIZE: 100,
  KEYS: {
    HOT_KEYWORDS: 'hotKeywords',
    CATEGORIES: 'categories',
    USER_INFO: 'userInfo',
    APP_CONFIG: 'appConfig'
  }
}

// 主题配置
const THEME_CONFIG = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
}

// 字体大小配置
const FONT_SIZE_CONFIG = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
}

// 语言配置
const LANGUAGE_CONFIG = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US'
}

// 分享平台
const SHARE_PLATFORMS = {
  WECHAT: 'wechat',
  MOMENTS: 'moments',
  QQ: 'qq',
  QZONE: 'qzone',
  WEIBO: 'weibo',
  COPY_LINK: 'copyLink'
}

// 分享平台显示名称
const SHARE_PLATFORM_NAMES = {
  [SHARE_PLATFORMS.WECHAT]: '微信好友',
  [SHARE_PLATFORMS.MOMENTS]: '朋友圈',
  [SHARE_PLATFORMS.QQ]: 'QQ好友',
  [SHARE_PLATFORMS.QZONE]: 'QQ空间',
  [SHARE_PLATFORMS.WEIBO]: '微博',
  [SHARE_PLATFORMS.COPY_LINK]: '复制链接'
}

// 举报类型
const REPORT_TYPES = {
  SPAM: 'spam',
  ILLEGAL: 'illegal',
  COPYRIGHT: 'copyright',
  INAPPROPRIATE: 'inappropriate',
  OTHER: 'other'
}

// 举报类型显示名称
const REPORT_TYPE_NAMES = {
  [REPORT_TYPES.SPAM]: '垃圾信息',
  [REPORT_TYPES.ILLEGAL]: '违法内容',
  [REPORT_TYPES.COPYRIGHT]: '版权侵犯',
  [REPORT_TYPES.INAPPROPRIATE]: '不当内容',
  [REPORT_TYPES.OTHER]: '其他'
}

// 评分等级
const RATING_LEVELS = {
  VERY_BAD: 1,
  BAD: 2,
  NORMAL: 3,
  GOOD: 4,
  VERY_GOOD: 5
}

// 评分等级显示名称
const RATING_LEVEL_NAMES = {
  [RATING_LEVELS.VERY_BAD]: '很差',
  [RATING_LEVELS.BAD]: '较差',
  [RATING_LEVELS.NORMAL]: '一般',
  [RATING_LEVELS.GOOD]: '较好',
  [RATING_LEVELS.VERY_GOOD]: '很好'
}

module.exports = {
  APP_INFO,
  DISK_TYPES,
  DISK_TYPE_NAMES,
  RESOURCE_CATEGORIES,
  RESOURCE_CATEGORY_NAMES,
  SORT_TYPES,
  SORT_TYPE_NAMES,
  USER_STATUS,
  USER_ROLES,
  GENDER,
  GENDER_NAMES,
  PAGE_PATHS,
  STORAGE_KEYS,
  EVENT_NAMES,
  ERROR_CODES,
  ERROR_MESSAGES,
  FILE_SIZE_UNITS,
  TIME_FORMATS,
  PAGINATION,
  SEARCH_CONFIG,
  UPLOAD_CONFIG,
  CACHE_CONFIG,
  THEME_CONFIG,
  FONT_SIZE_CONFIG,
  LANGUAGE_CONFIG,
  SHARE_PLATFORMS,
  SHARE_PLATFORM_NAMES,
  REPORT_TYPES,
  REPORT_TYPE_NAMES,
  RATING_LEVELS,
  RATING_LEVEL_NAMES
}
