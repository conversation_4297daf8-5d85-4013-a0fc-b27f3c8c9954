// 搜索相关API接口
const request = require('../utils/request')

/**
 * 搜索资源
 * @param {object} params 搜索参数
 */
function searchResources(params) {
  return request.get('/search', params)
}

/**
 * 获取热门搜索词
 * @param {number} limit 数量限制
 */
function getHotKeywords(limit = 10) {
  return request.get('/search/hot-keywords', { limit })
}

/**
 * 获取搜索建议
 * @param {string} keyword 关键词
 * @param {number} limit 数量限制
 */
function getSearchSuggestions(keyword, limit = 10) {
  return request.get('/search/suggestions', { keyword, limit })
}

/**
 * 高级搜索
 * @param {object} params 搜索参数
 */
function advancedSearch(params) {
  return request.post('/search/advanced', params)
}

/**
 * 按分类搜索
 * @param {object} params 搜索参数
 */
function searchByCategory(params) {
  return request.get('/search/category', params)
}

/**
 * 按网盘类型搜索
 * @param {object} params 搜索参数
 */
function searchByDiskType(params) {
  return request.get('/search/disk-type', params)
}

/**
 * 获取搜索过滤选项
 */
function getSearchFilters() {
  return request.get('/search/filters')
}

/**
 * 搜索用户
 * @param {object} params 搜索参数
 */
function searchUsers(params) {
  return request.get('/search/users', params)
}

/**
 * 相关搜索
 * @param {string} keyword 关键词
 * @param {number} limit 数量限制
 */
function getRelatedSearches(keyword, limit = 10) {
  return request.get('/search/related', { keyword, limit })
}

/**
 * 搜索统计
 * @param {string} keyword 关键词
 */
function recordSearchStats(keyword) {
  return request.post('/search/stats', { keyword })
}

/**
 * 获取搜索趋势
 * @param {object} params 查询参数
 */
function getSearchTrends(params = {}) {
  return request.get('/search/trends', params)
}

/**
 * 保存搜索
 * @param {object} data 搜索数据
 */
function saveSearch(data) {
  return request.post('/search/save', data)
}

/**
 * 获取保存的搜索
 * @param {object} params 查询参数
 */
function getSavedSearches(params = {}) {
  return request.get('/search/saved', params)
}

/**
 * 删除保存的搜索
 * @param {string} id 搜索ID
 */
function deleteSavedSearch(id) {
  return request.delete(`/search/saved/${id}`)
}

/**
 * 搜索自动完成
 * @param {string} keyword 关键词
 * @param {number} limit 数量限制
 */
function getAutoComplete(keyword, limit = 10) {
  return request.get('/search/autocomplete', { keyword, limit })
}

/**
 * 获取搜索历史统计
 * @param {object} params 查询参数
 */
function getSearchHistoryStats(params = {}) {
  return request.get('/search/history-stats', params)
}

/**
 * 举报搜索结果
 * @param {object} data 举报数据
 */
function reportSearchResult(data) {
  return request.post('/search/report', data)
}

/**
 * 获取搜索排行榜
 * @param {object} params 查询参数
 */
function getSearchRanking(params = {}) {
  return request.get('/search/ranking', params)
}

/**
 * 智能搜索
 * @param {object} params 搜索参数
 */
function smartSearch(params) {
  return request.post('/search/smart', params)
}

/**
 * 语音搜索
 * @param {string} audioPath 音频文件路径
 */
function voiceSearch(audioPath) {
  return request.upload('/search/voice', audioPath, {
    name: 'audio'
  })
}

/**
 * 图片搜索
 * @param {string} imagePath 图片文件路径
 */
function imageSearch(imagePath) {
  return request.upload('/search/image', imagePath, {
    name: 'image'
  })
}

/**
 * 获取搜索配置
 */
function getSearchConfig() {
  return request.get('/search/config')
}

/**
 * 更新搜索配置
 * @param {object} data 配置数据
 */
function updateSearchConfig(data) {
  return request.put('/search/config', data)
}

module.exports = {
  searchResources,
  getHotKeywords,
  getSearchSuggestions,
  advancedSearch,
  searchByCategory,
  searchByDiskType,
  getSearchFilters,
  searchUsers,
  getRelatedSearches,
  recordSearchStats,
  getSearchTrends,
  saveSearch,
  getSavedSearches,
  deleteSavedSearch,
  getAutoComplete,
  getSearchHistoryStats,
  reportSearchResult,
  getSearchRanking,
  smartSearch,
  voiceSearch,
  imageSearch,
  getSearchConfig,
  updateSearchConfig
}
