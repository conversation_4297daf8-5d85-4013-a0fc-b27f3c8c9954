// 通用API接口
const request = require('../utils/request')

/**
 * 获取应用配置
 */
function getAppConfig() {
  return request.get('/common/config')
}

/**
 * 获取版本信息
 */
function getVersionInfo() {
  return request.get('/common/version')
}

/**
 * 检查更新
 */
function checkUpdate() {
  return request.get('/common/check-update')
}

/**
 * 获取公告列表
 * @param {object} params 查询参数
 */
function getNotices(params = {}) {
  return request.get('/common/notices', params)
}

/**
 * 获取公告详情
 * @param {string} id 公告ID
 */
function getNoticeDetail(id) {
  return request.get(`/common/notices/${id}`)
}

/**
 * 标记公告已读
 * @param {string} id 公告ID
 */
function markNoticeRead(id) {
  return request.post(`/common/notices/${id}/read`)
}

/**
 * 获取轮播图
 */
function getBanners() {
  return request.get('/common/banners')
}

/**
 * 获取帮助文档
 * @param {object} params 查询参数
 */
function getHelpDocs(params = {}) {
  return request.get('/common/help', params)
}

/**
 * 获取帮助文档详情
 * @param {string} id 文档ID
 */
function getHelpDocDetail(id) {
  return request.get(`/common/help/${id}`)
}

/**
 * 获取常见问题
 */
function getFAQ() {
  return request.get('/common/faq')
}

/**
 * 获取联系方式
 */
function getContactInfo() {
  return request.get('/common/contact')
}

/**
 * 提交意见反馈
 * @param {object} data 反馈数据
 */
function submitFeedback(data) {
  return request.post('/common/feedback', data)
}

/**
 * 上传文件
 * @param {string} filePath 文件路径
 * @param {object} options 上传选项
 */
function uploadFile(filePath, options = {}) {
  return request.upload('/common/upload', filePath, {
    name: 'file',
    ...options
  })
}

/**
 * 上传图片
 * @param {string} imagePath 图片路径
 * @param {object} options 上传选项
 */
function uploadImage(imagePath, options = {}) {
  return request.upload('/common/upload/image', imagePath, {
    name: 'image',
    ...options
  })
}

/**
 * 获取上传token
 * @param {string} type 文件类型
 */
function getUploadToken(type = 'image') {
  return request.get('/common/upload/token', { type })
}

/**
 * 获取地区列表
 */
function getRegions() {
  return request.get('/common/regions')
}

/**
 * 获取城市列表
 * @param {string} provinceCode 省份代码
 */
function getCities(provinceCode) {
  return request.get('/common/cities', { provinceCode })
}

/**
 * 获取区县列表
 * @param {string} cityCode 城市代码
 */
function getDistricts(cityCode) {
  return request.get('/common/districts', { cityCode })
}

/**
 * 获取系统时间
 */
function getSystemTime() {
  return request.get('/common/time')
}

/**
 * 获取系统状态
 */
function getSystemStatus() {
  return request.get('/common/status')
}

/**
 * 获取统计数据
 * @param {object} params 查询参数
 */
function getStatistics(params = {}) {
  return request.get('/common/statistics', params)
}

/**
 * 记录用户行为
 * @param {object} data 行为数据
 */
function recordUserBehavior(data) {
  return request.post('/common/behavior', data)
}

/**
 * 获取IP信息
 */
function getIPInfo() {
  return request.get('/common/ip-info')
}

/**
 * 获取天气信息
 * @param {object} params 查询参数
 */
function getWeatherInfo(params = {}) {
  return request.get('/common/weather', params)
}

/**
 * 短链接生成
 * @param {string} url 原始链接
 */
function generateShortUrl(url) {
  return request.post('/common/short-url', { url })
}

/**
 * 短链接解析
 * @param {string} shortUrl 短链接
 */
function parseShortUrl(shortUrl) {
  return request.get('/common/short-url/parse', { shortUrl })
}

/**
 * 二维码生成
 * @param {object} data 二维码数据
 */
function generateQRCode(data) {
  return request.post('/common/qrcode', data)
}

/**
 * 发送邮件
 * @param {object} data 邮件数据
 */
function sendEmail(data) {
  return request.post('/common/email', data)
}

/**
 * 发送短信
 * @param {object} data 短信数据
 */
function sendSMS(data) {
  return request.post('/common/sms', data)
}

/**
 * 获取验证码
 * @param {object} params 参数
 */
function getCaptcha(params = {}) {
  return request.get('/common/captcha', params)
}

/**
 * 验证验证码
 * @param {object} data 验证数据
 */
function verifyCaptcha(data) {
  return request.post('/common/captcha/verify', data)
}

/**
 * 获取敏感词列表
 */
function getSensitiveWords() {
  return request.get('/common/sensitive-words')
}

/**
 * 内容审核
 * @param {object} data 审核数据
 */
function contentAudit(data) {
  return request.post('/common/audit', data)
}

/**
 * 获取分享配置
 */
function getShareConfig() {
  return request.get('/common/share/config')
}

/**
 * 分享统计
 * @param {object} data 分享数据
 */
function recordShareStats(data) {
  return request.post('/common/share/stats', data)
}

/**
 * 获取推广链接
 * @param {object} params 参数
 */
function getPromotionLink(params = {}) {
  return request.get('/common/promotion/link', params)
}

/**
 * 推广统计
 * @param {object} data 推广数据
 */
function recordPromotionStats(data) {
  return request.post('/common/promotion/stats', data)
}

module.exports = {
  getAppConfig,
  getVersionInfo,
  checkUpdate,
  getNotices,
  getNoticeDetail,
  markNoticeRead,
  getBanners,
  getHelpDocs,
  getHelpDocDetail,
  getFAQ,
  getContactInfo,
  submitFeedback,
  uploadFile,
  uploadImage,
  getUploadToken,
  getRegions,
  getCities,
  getDistricts,
  getSystemTime,
  getSystemStatus,
  getStatistics,
  recordUserBehavior,
  getIPInfo,
  getWeatherInfo,
  generateShortUrl,
  parseShortUrl,
  generateQRCode,
  sendEmail,
  sendSMS,
  getCaptcha,
  verifyCaptcha,
  getSensitiveWords,
  contentAudit,
  getShareConfig,
  recordShareStats,
  getPromotionLink,
  recordPromotionStats
}
