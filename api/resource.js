// 资源相关API接口
const request = require('../utils/request')

/**
 * 获取资源详情
 * @param {string} id 资源ID
 */
function getResourceDetail(id) {
  return request.get(`/resource/${id}`)
}

/**
 * 获取资源列表
 * @param {object} params 查询参数
 */
function getResourceList(params = {}) {
  return request.get('/resource/list', params)
}

/**
 * 获取推荐资源
 * @param {object} params 查询参数
 */
function getRecommendedResources(params = {}) {
  return request.get('/resource/recommended', params)
}

/**
 * 获取热门资源
 * @param {object} params 查询参数
 */
function getHotResources(params = {}) {
  return request.get('/resource/hot', params)
}

/**
 * 获取最新资源
 * @param {object} params 查询参数
 */
function getLatestResources(params = {}) {
  return request.get('/resource/latest', params)
}

/**
 * 获取相关资源
 * @param {string} id 资源ID
 * @param {object} params 查询参数
 */
function getRelatedResources(id, params = {}) {
  return request.get(`/resource/${id}/related`, params)
}

/**
 * 获取资源分类
 */
function getResourceCategories() {
  return request.get('/resource/categories')
}

/**
 * 按分类获取资源
 * @param {string} categoryId 分类ID
 * @param {object} params 查询参数
 */
function getResourcesByCategory(categoryId, params = {}) {
  return request.get(`/resource/category/${categoryId}`, params)
}

/**
 * 获取资源标签
 */
function getResourceTags() {
  return request.get('/resource/tags')
}

/**
 * 按标签获取资源
 * @param {string} tag 标签
 * @param {object} params 查询参数
 */
function getResourcesByTag(tag, params = {}) {
  return request.get('/resource/tag', { tag, ...params })
}

/**
 * 收藏资源
 * @param {string} id 资源ID
 */
function favoriteResource(id) {
  return request.post(`/resource/${id}/favorite`)
}

/**
 * 取消收藏资源
 * @param {string} id 资源ID
 */
function unfavoriteResource(id) {
  return request.delete(`/resource/${id}/favorite`)
}

/**
 * 检查是否已收藏
 * @param {string} id 资源ID
 */
function checkFavoriteStatus(id) {
  return request.get(`/resource/${id}/favorite/status`)
}

/**
 * 分享资源
 * @param {string} id 资源ID
 * @param {object} data 分享数据
 */
function shareResource(id, data = {}) {
  return request.post(`/resource/${id}/share`, data)
}

/**
 * 举报资源
 * @param {string} id 资源ID
 * @param {object} data 举报数据
 */
function reportResource(id, data) {
  return request.post(`/resource/${id}/report`, data)
}

/**
 * 评价资源
 * @param {string} id 资源ID
 * @param {object} data 评价数据
 */
function rateResource(id, data) {
  return request.post(`/resource/${id}/rate`, data)
}

/**
 * 获取资源评价
 * @param {string} id 资源ID
 * @param {object} params 查询参数
 */
function getResourceRatings(id, params = {}) {
  return request.get(`/resource/${id}/ratings`, params)
}

/**
 * 获取资源评论
 * @param {string} id 资源ID
 * @param {object} params 查询参数
 */
function getResourceComments(id, params = {}) {
  return request.get(`/resource/${id}/comments`, params)
}

/**
 * 添加资源评论
 * @param {string} id 资源ID
 * @param {object} data 评论数据
 */
function addResourceComment(id, data) {
  return request.post(`/resource/${id}/comments`, data)
}

/**
 * 删除资源评论
 * @param {string} resourceId 资源ID
 * @param {string} commentId 评论ID
 */
function deleteResourceComment(resourceId, commentId) {
  return request.delete(`/resource/${resourceId}/comments/${commentId}`)
}

/**
 * 点赞资源
 * @param {string} id 资源ID
 */
function likeResource(id) {
  return request.post(`/resource/${id}/like`)
}

/**
 * 取消点赞资源
 * @param {string} id 资源ID
 */
function unlikeResource(id) {
  return request.delete(`/resource/${id}/like`)
}

/**
 * 检查点赞状态
 * @param {string} id 资源ID
 */
function checkLikeStatus(id) {
  return request.get(`/resource/${id}/like/status`)
}

/**
 * 增加浏览次数
 * @param {string} id 资源ID
 */
function incrementViewCount(id) {
  return request.post(`/resource/${id}/view`)
}

/**
 * 获取资源统计
 * @param {string} id 资源ID
 */
function getResourceStats(id) {
  return request.get(`/resource/${id}/stats`)
}

/**
 * 获取资源历史版本
 * @param {string} id 资源ID
 */
function getResourceHistory(id) {
  return request.get(`/resource/${id}/history`)
}

/**
 * 下载资源
 * @param {string} id 资源ID
 * @param {object} data 下载数据
 */
function downloadResource(id, data = {}) {
  return request.post(`/resource/${id}/download`, data)
}

/**
 * 获取下载链接
 * @param {string} id 资源ID
 */
function getDownloadLink(id) {
  return request.get(`/resource/${id}/download-link`)
}

/**
 * 验证资源链接
 * @param {string} id 资源ID
 */
function validateResourceLink(id) {
  return request.post(`/resource/${id}/validate`)
}

/**
 * 获取资源预览
 * @param {string} id 资源ID
 */
function getResourcePreview(id) {
  return request.get(`/resource/${id}/preview`)
}

/**
 * 提交资源
 * @param {object} data 资源数据
 */
function submitResource(data) {
  return request.post('/resource/submit', data)
}

/**
 * 更新资源
 * @param {string} id 资源ID
 * @param {object} data 资源数据
 */
function updateResource(id, data) {
  return request.put(`/resource/${id}`, data)
}

/**
 * 删除资源
 * @param {string} id 资源ID
 */
function deleteResource(id) {
  return request.delete(`/resource/${id}`)
}

module.exports = {
  getResourceDetail,
  getResourceList,
  getRecommendedResources,
  getHotResources,
  getLatestResources,
  getRelatedResources,
  getResourceCategories,
  getResourcesByCategory,
  getResourceTags,
  getResourcesByTag,
  favoriteResource,
  unfavoriteResource,
  checkFavoriteStatus,
  shareResource,
  reportResource,
  rateResource,
  getResourceRatings,
  getResourceComments,
  addResourceComment,
  deleteResourceComment,
  likeResource,
  unlikeResource,
  checkLikeStatus,
  incrementViewCount,
  getResourceStats,
  getResourceHistory,
  downloadResource,
  getDownloadLink,
  validateResourceLink,
  getResourcePreview,
  submitResource,
  updateResource,
  deleteResource
}
