// 用户相关API接口
const request = require('../utils/request')

/**
 * 用户登录
 * @param {object} data 登录数据
 */
function login(data) {
  return request.post('/auth/login', data)
}

/**
 * 用户注册
 * @param {object} data 注册数据
 */
function register(data) {
  return request.post('/auth/register', data)
}

/**
 * 获取用户信息
 */
function getUserInfo() {
  return request.get('/user/info')
}

/**
 * 更新用户信息
 * @param {object} data 用户信息
 */
function updateUserInfo(data) {
  return request.put('/user/info', data)
}

/**
 * 上传头像
 * @param {string} filePath 文件路径
 */
function uploadAvatar(filePath) {
  return request.upload('/user/avatar', filePath, {
    name: 'avatar'
  })
}

/**
 * 绑定手机号
 * @param {object} data 手机号和验证码
 */
function bindPhone(data) {
  return request.post('/user/bind-phone', data)
}

/**
 * 发送验证码
 * @param {string} phone 手机号
 */
function sendCode(phone) {
  return request.post('/user/send-code', { phone })
}

/**
 * 验证验证码
 * @param {object} data 手机号和验证码
 */
function verifyCode(data) {
  return request.post('/user/verify-code', data)
}

/**
 * 修改密码
 * @param {object} data 密码数据
 */
function changePassword(data) {
  return request.put('/user/password', data)
}

/**
 * 重置密码
 * @param {object} data 重置数据
 */
function resetPassword(data) {
  return request.post('/user/reset-password', data)
}

/**
 * 获取用户统计信息
 */
function getUserStats() {
  return request.get('/user/stats')
}

/**
 * 获取用户收藏列表
 * @param {object} params 查询参数
 */
function getFavorites(params = {}) {
  return request.get('/user/favorites', params)
}

/**
 * 添加收藏
 * @param {object} data 收藏数据
 */
function addFavorite(data) {
  return request.post('/user/favorites', data)
}

/**
 * 取消收藏
 * @param {string} id 资源ID
 */
function removeFavorite(id) {
  return request.delete(`/user/favorites/${id}`)
}

/**
 * 获取搜索历史
 * @param {object} params 查询参数
 */
function getSearchHistory(params = {}) {
  return request.get('/user/search-history', params)
}

/**
 * 添加搜索历史
 * @param {object} data 搜索数据
 */
function addSearchHistory(data) {
  return request.post('/user/search-history', data)
}

/**
 * 清空搜索历史
 */
function clearSearchHistory() {
  return request.delete('/user/search-history')
}

/**
 * 获取用户设置
 */
function getUserSettings() {
  return request.get('/user/settings')
}

/**
 * 更新用户设置
 * @param {object} data 设置数据
 */
function updateUserSettings(data) {
  return request.put('/user/settings', data)
}

/**
 * 注销账号
 * @param {object} data 注销数据
 */
function deleteAccount(data) {
  return request.post('/user/delete-account', data)
}

/**
 * 用户反馈
 * @param {object} data 反馈数据
 */
function submitFeedback(data) {
  return request.post('/user/feedback', data)
}

/**
 * 获取反馈列表
 * @param {object} params 查询参数
 */
function getFeedbackList(params = {}) {
  return request.get('/user/feedback', params)
}

/**
 * 用户签到
 */
function checkIn() {
  return request.post('/user/check-in')
}

/**
 * 获取签到状态
 */
function getCheckInStatus() {
  return request.get('/user/check-in/status')
}

/**
 * 获取积分记录
 * @param {object} params 查询参数
 */
function getPointsHistory(params = {}) {
  return request.get('/user/points', params)
}

/**
 * 兑换积分
 * @param {object} data 兑换数据
 */
function exchangePoints(data) {
  return request.post('/user/points/exchange', data)
}

module.exports = {
  login,
  register,
  getUserInfo,
  updateUserInfo,
  uploadAvatar,
  bindPhone,
  sendCode,
  verifyCode,
  changePassword,
  resetPassword,
  getUserStats,
  getFavorites,
  addFavorite,
  removeFavorite,
  getSearchHistory,
  addSearchHistory,
  clearSearchHistory,
  getUserSettings,
  updateUserSettings,
  deleteAccount,
  submitFeedback,
  getFeedbackList,
  checkIn,
  getCheckInStatus,
  getPointsHistory,
  exchangePoints
}
