<!--components/loading/loading.wxml-->
<view class="loading-container {{show ? 'loading-show' : 'loading-hide'}}" wx:if="{{show}}">
  <!-- 遮罩层 -->
  <view class="loading-mask" wx:if="{{mask}}" bindtap="onMaskTap"></view>
  
  <!-- 加载内容 -->
  <view class="loading-content loading-{{size}}">
    <!-- 加载动画 -->
    <view class="loading-spinner loading-{{type}}" wx:if="{{type === 'spinner'}}">
      <view class="spinner-item" wx:for="{{12}}" wx:key="index" style="animation-delay: {{index * 0.1}}s; border-color: {{color}};"></view>
    </view>
    
    <view class="loading-dots" wx:elif="{{type === 'dots'}}">
      <view class="dot-item" wx:for="{{3}}" wx:key="index" style="animation-delay: {{index * 0.2}}s; background-color: {{color}};"></view>
    </view>
    
    <view class="loading-circle" wx:elif="{{type === 'circle'}}" style="border-color: {{color}} transparent transparent transparent;">
    </view>
    
    <!-- 加载文本 -->
    <text class="loading-text" wx:if="{{showText && text}}">{{text}}</text>
  </view>
</view>
