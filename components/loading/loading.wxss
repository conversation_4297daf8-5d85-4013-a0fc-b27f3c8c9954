/* components/loading/loading.wxss */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.loading-show {
  opacity: 1;
  pointer-events: auto;
}

.loading-hide {
  opacity: 0;
  pointer-events: none;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

.loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 40rpx;
  backdrop-filter: blur(10px);
}

.loading-small {
  padding: 20rpx;
}

.loading-medium {
  padding: 40rpx;
}

.loading-large {
  padding: 60rpx;
}

/* Spinner加载动画 */
.loading-spinner {
  position: relative;
  width: 60rpx;
  height: 60rpx;
}

.loading-small .loading-spinner {
  width: 40rpx;
  height: 40rpx;
}

.loading-large .loading-spinner {
  width: 80rpx;
  height: 80rpx;
}

.spinner-item {
  position: absolute;
  width: 4rpx;
  height: 16rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
  animation: spinner-fade 1.2s linear infinite;
  transform-origin: 50% 30rpx;
}

.loading-small .spinner-item {
  width: 3rpx;
  height: 12rpx;
  transform-origin: 50% 20rpx;
}

.loading-large .spinner-item {
  width: 5rpx;
  height: 20rpx;
  transform-origin: 50% 40rpx;
}

.spinner-item:nth-child(1) { transform: rotate(0deg); }
.spinner-item:nth-child(2) { transform: rotate(30deg); }
.spinner-item:nth-child(3) { transform: rotate(60deg); }
.spinner-item:nth-child(4) { transform: rotate(90deg); }
.spinner-item:nth-child(5) { transform: rotate(120deg); }
.spinner-item:nth-child(6) { transform: rotate(150deg); }
.spinner-item:nth-child(7) { transform: rotate(180deg); }
.spinner-item:nth-child(8) { transform: rotate(210deg); }
.spinner-item:nth-child(9) { transform: rotate(240deg); }
.spinner-item:nth-child(10) { transform: rotate(270deg); }
.spinner-item:nth-child(11) { transform: rotate(300deg); }
.spinner-item:nth-child(12) { transform: rotate(330deg); }

@keyframes spinner-fade {
  0%, 39%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

/* Dots加载动画 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.dot-item {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #1890ff;
  animation: dots-bounce 1.4s ease-in-out infinite both;
}

.loading-small .dot-item {
  width: 8rpx;
  height: 8rpx;
}

.loading-large .dot-item {
  width: 16rpx;
  height: 16rpx;
}

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Circle加载动画 */
.loading-circle {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid;
  border-color: #1890ff transparent transparent transparent;
  border-radius: 50%;
  animation: circle-spin 1s linear infinite;
}

.loading-small .loading-circle {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.loading-large .loading-circle {
  width: 80rpx;
  height: 80rpx;
  border-width: 5rpx;
}

@keyframes circle-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 加载文本 */
.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.loading-small .loading-text {
  margin-top: 16rpx;
  font-size: 24rpx;
}

.loading-large .loading-text {
  margin-top: 24rpx;
  font-size: 32rpx;
}
