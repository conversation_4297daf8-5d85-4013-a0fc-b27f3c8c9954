// components/loading/loading.js
Component({
  properties: {
    // 是否显示加载
    show: {
      type: Boolean,
      value: false
    },
    
    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },
    
    // 加载类型
    type: {
      type: String,
      value: 'spinner' // spinner, dots, circle
    },
    
    // 大小
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    
    // 颜色
    color: {
      type: String,
      value: '#1890ff'
    },
    
    // 是否显示遮罩
    mask: {
      type: Boolean,
      value: false
    },
    
    // 是否显示文本
    showText: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 组件内部数据
  },

  methods: {
    // 点击遮罩
    onMaskTap() {
      // 如果允许点击遮罩关闭，可以触发事件
      this.triggerEvent('maskclick')
    }
  }
})
