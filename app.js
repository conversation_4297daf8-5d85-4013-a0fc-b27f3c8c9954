// app.js
const authUtils = require('./utils/auth')
const storageUtils = require('./utils/storage')

App({
  globalData: {
    userInfo: null,
    isLogin: false,
    baseUrl: 'https://api.example.com', // 后端API地址
    version: '1.0.0'
  },

  onLaunch() {
    console.log('云搜罗小程序启动')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 获取系统信息
    this.getSystemInfo()
    
    // 检查更新
    this.checkUpdate()
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onError(msg) {
    console.error('小程序错误:', msg)
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = storageUtils.getToken()
    const userInfo = storageUtils.getUserInfo()
    
    if (token && userInfo) {
      this.globalData.isLogin = true
      this.globalData.userInfo = userInfo
    } else {
      this.globalData.isLogin = false
      this.globalData.userInfo = null
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 检查更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败')
      })
    }
  },

  // 全局登录方法
  login() {
    return new Promise((resolve, reject) => {
      authUtils.login()
        .then((userInfo) => {
          this.globalData.isLogin = true
          this.globalData.userInfo = userInfo
          resolve(userInfo)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // 全局退出登录方法
  logout() {
    storageUtils.clearAuth()
    this.globalData.isLogin = false
    this.globalData.userInfo = null
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 检查登录状态并跳转
  checkAuth() {
    if (!this.globalData.isLogin) {
      wx.navigateTo({
        url: '/pages/login/login'
      })
      return false
    }
    return true
  }
})
