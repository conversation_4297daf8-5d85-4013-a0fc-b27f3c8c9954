.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-pull-down-refresh {
  overflow: hidden;
  height: 100%;
}
.t-pull-down-refresh__track {
  position: relative;
}
.t-pull-down-refresh__track--loosing {
  transition: transform ease 0.24s;
}
.t-pull-down-refresh__tips {
  position: absolute;
  color: var(--td-pull-down-refresh-color, var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4))));
  font-size: 24rpx;
  top: 0;
  width: 100%;
  transform: translateY(-100%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
}
.t-pull-down-refresh__tips--loosing {
  transition: height ease 0.24s;
}
.t-pull-down-refresh__text {
  margin: 16rpx 0 0;
}
