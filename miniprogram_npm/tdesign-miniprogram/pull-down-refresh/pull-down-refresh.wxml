<wxs src="../common/utils.wxs" module="_" />

<scroll-view
  style="{{_._style([style, customStyle, 'max-height: calc(100vh - ' + distanceTop + 'px)'])}}"
  class="{{classPrefix}} class {{prefix}}-class"
  type="list"
  scroll-top="{{scrollTop}}"
  scroll-y
  enable-back-to-top="{{enableBackToTop}}"
  enable-passive="{{enablePassive}}"
  lower-threshold="{{lowerThreshold}}"
  upper-threshold="{{upperThreshold}}"
  scroll-into-view="{{scrollIntoView}}"
  show-scrollbar="{{showScrollbar}}"
  enhanced
  scroll-with-animation
  bounces="{{false}}"
  bind:touchstart="onTouchStart"
  bind:touchmove="onTouchMove"
  bind:touchend="onTouchEnd"
  bind:scroll="onScroll"
  binddragstart="onDragStart"
  binddragging="onDragging"
  binddragend="onDragEnd"
  bindscrolltoupper="onScrollToTop"
  bindscrolltolower="onScrollToBottom"
  throttle="{{false}}"
>
  <slot name="header" />

  <view
    class="{{classPrefix}}__track {{classPrefix + '__track--' + (loosing ? 'loosing' : '')}}"
    style="{{barHeight > 0 ? 'transform: translate3d(0, ' + barHeight + 'px, 0);' : ''}}"
  >
    <view
      class="{{classPrefix}}__tips {{classPrefix + '__tips--' + (loosing ? 'loosing' : '')}}"
      style="height: {{tipsHeight}}px"
      aria-live="polite"
    >
      <t-loading
        wx:if="{{refreshStatus === 2}}"
        delay="{{loadingProps.delay || 0}}"
        duration="{{loadingProps.duration || 800}}"
        indicator="{{loadingProps.indicator || true}}"
        layout="{{loadingProps.layout || 'horizontal'}}"
        loading="{{loadingProps.loading || true}}"
        pause="{{loadingProps.pause || false}}"
        progress="{{loadingProps.progress || 0}}"
        reverse="{{loadingProps.reverse || false}}"
        size="{{loadingProps.size || '50rpx'}}"
        text="{{loadingProps.text || loadingTexts[refreshStatus]}}"
        theme="{{loadingProps.theme || 'circular'}}"
        t-class-indicator="{{prefix}}-class-indicator"
      />
      <view wx:elif="{{refreshStatus >= 0}}" class="{{classPrefix}}__text {{prefix}}-class-text"
        >{{loadingTexts[refreshStatus]}}</view
      >
    </view>
    <slot />
  </view>
</scroll-view>
