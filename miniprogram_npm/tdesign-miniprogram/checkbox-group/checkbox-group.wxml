<wxs src="../common/utils.wxs" module="_" />

<view class="{{ classPrefix }} class {{prefix}}-class" style="{{_._style([style, customStyle])}}">
  <slot />
  <block wx:for="{{checkboxOptions}}" wx:key="value">
    <t-checkbox
      class="{{prefix}}-checkbox-option"
      data-item="{{item}}"
      label="{{item.label || item.text || ''}}"
      value="{{item.value == null ? '' : item.value}}"
      block="{{item.block || true}}"
      check-all="{{item.checkAll || false}}"
      checked="{{item.checked || false}}"
      content="{{item.content || ''}}"
      content-disabled="{{item.contentDisabled || false}}"
      icon="{{item.icon || 'circle'}}"
      indeterminate="{{item.indeterminate || false}}"
      disabled="{{ item.disabled == null ? disabled : item.disabled }}"
      max-content-row="{{item.maxContentRow || 5}}"
      max-label-row="{{item.maxLabelRow || 3}}"
      name="{{item.name || ''}}"
      borderless="{{borderless}}"
      readonly="{{item.readonly || false}}"
      bind:change="handleInnerChildChange"
      placement="{{item.placement || 'left'}}"
    />
  </block>
</view>
