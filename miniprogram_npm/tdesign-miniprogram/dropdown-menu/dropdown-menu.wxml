<import src="../common/template/icon.wxml" />
<wxs src="../common/utils.wxs" module="_" />

<view
  style="{{_._style([style, customStyle])}}"
  class="{{ classPrefix }} class {{prefix}}-class"
  id="t-bar"
  catchtouchmove="{{activeIdx === -1?'':'noop'}}"
>
  <view
    wx:for="{{menus}}"
    wx:key="index"
    bindtap="handleToggle"
    data-index="{{index}}"
    class="{{_.cls(classPrefix + '__item', [['active', activeIdx == index], ['disabled', item.disabled], [index, true]])}} {{prefix}}-class-item"
    aria-disabled="{{item.disabled}}"
    aria-role="button"
    aria-expanded="{{activeIdx === index}}"
    aria-haspopup="menu"
  >
    <view class="{{classPrefix}}__title {{prefix}}-class-label">{{item.label}}</view>
    <template
      is="icon"
      data="{{..._arrowIcon, ariaHidden: true, tClass: classPrefix + '__icon ' + classPrefix + '__icon--' + (activeIdx == index ? 'active ' : ' ') + prefix + '-class-icon'}}"
    />
  </view>
  <slot />
</view>
