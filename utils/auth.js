// 认证相关工具
const request = require('./request')
const storageUtils = require('./storage')

/**
 * 微信登录
 */
function login() {
  return new Promise((resolve, reject) => {
    // 检查登录状态
    wx.checkSession({
      success: () => {
        // session有效，检查本地是否有用户信息
        const userInfo = storageUtils.getUserInfo()
        const token = storageUtils.getToken()
        
        if (userInfo && token) {
          resolve(userInfo)
          return
        }
        
        // 本地无用户信息，重新获取
        performLogin().then(resolve).catch(reject)
      },
      fail: () => {
        // session失效，重新登录
        performLogin().then(resolve).catch(reject)
      }
    })
  })
}

/**
 * 执行登录流程
 */
function performLogin() {
  return new Promise((resolve, reject) => {
    // 1. 获取登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 2. 发送code到后端
          request.post('/auth/login', {
            code: loginRes.code
          }, {
            showLoading: true,
            loadingText: '登录中...'
          })
          .then((result) => {
            // 3. 保存token和用户信息
            if (result.token) {
              storageUtils.setToken(result.token)
            }
            
            if (result.userInfo) {
              storageUtils.setUserInfo(result.userInfo)
              resolve(result.userInfo)
            } else {
              // 需要获取用户信息
              getUserProfile().then(resolve).catch(reject)
            }
          })
          .catch(reject)
        } else {
          reject(new Error('获取登录凭证失败'))
        }
      },
      fail: (error) => {
        reject(new Error('微信登录失败'))
      }
    })
  })
}

/**
 * 获取用户信息
 */
function getUserProfile() {
  return new Promise((resolve, reject) => {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = res.userInfo
        
        // 发送用户信息到后端
        request.post('/auth/userinfo', {
          userInfo: userInfo,
          rawData: res.rawData,
          signature: res.signature,
          encryptedData: res.encryptedData,
          iv: res.iv
        })
        .then((result) => {
          // 保存用户信息
          const completeUserInfo = result.userInfo || userInfo
          storageUtils.setUserInfo(completeUserInfo)
          resolve(completeUserInfo)
        })
        .catch(reject)
      },
      fail: (error) => {
        reject(new Error('获取用户信息失败'))
      }
    })
  })
}

/**
 * 获取手机号
 */
function getPhoneNumber(e) {
  return new Promise((resolve, reject) => {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 发送加密数据到后端解密
      request.post('/auth/phone', {
        encryptedData: e.detail.encryptedData,
        iv: e.detail.iv
      })
      .then((result) => {
        // 更新用户信息
        const userInfo = storageUtils.getUserInfo()
        if (userInfo && result.phoneNumber) {
          userInfo.phoneNumber = result.phoneNumber
          storageUtils.setUserInfo(userInfo)
        }
        resolve(result.phoneNumber)
      })
      .catch(reject)
    } else {
      reject(new Error('获取手机号失败'))
    }
  })
}

/**
 * 检查登录状态
 */
function checkLoginStatus() {
  const token = storageUtils.getToken()
  const userInfo = storageUtils.getUserInfo()
  
  return !!(token && userInfo)
}

/**
 * 退出登录
 */
function logout() {
  return new Promise((resolve, reject) => {
    const token = storageUtils.getToken()
    
    if (token) {
      // 通知后端退出登录
      request.post('/auth/logout', {}, {
        showLoading: false,
        showError: false
      })
      .finally(() => {
        // 清除本地数据
        storageUtils.clearAuth()
        resolve()
      })
    } else {
      // 清除本地数据
      storageUtils.clearAuth()
      resolve()
    }
  })
}

/**
 * 刷新token
 */
function refreshToken() {
  return new Promise((resolve, reject) => {
    const token = storageUtils.getToken()
    
    if (!token) {
      reject(new Error('无有效token'))
      return
    }
    
    request.post('/auth/refresh', {}, {
      showLoading: false,
      showError: false
    })
    .then((result) => {
      if (result.token) {
        storageUtils.setToken(result.token)
        resolve(result.token)
      } else {
        reject(new Error('刷新token失败'))
      }
    })
    .catch(reject)
  })
}

/**
 * 检查是否需要登录
 */
function requireAuth() {
  return new Promise((resolve, reject) => {
    if (checkLoginStatus()) {
      resolve()
    } else {
      // 跳转到登录页
      wx.navigateTo({
        url: '/pages/login/login'
      })
      reject(new Error('需要登录'))
    }
  })
}

/**
 * 更新用户信息
 */
function updateUserInfo(userInfo) {
  return new Promise((resolve, reject) => {
    request.post('/auth/update', userInfo)
    .then((result) => {
      // 更新本地用户信息
      const localUserInfo = storageUtils.getUserInfo()
      const updatedUserInfo = { ...localUserInfo, ...result.userInfo }
      storageUtils.setUserInfo(updatedUserInfo)
      resolve(updatedUserInfo)
    })
    .catch(reject)
  })
}

/**
 * 绑定手机号
 */
function bindPhoneNumber(phoneNumber, code) {
  return new Promise((resolve, reject) => {
    request.post('/auth/bind-phone', {
      phoneNumber,
      code
    })
    .then((result) => {
      // 更新本地用户信息
      const userInfo = storageUtils.getUserInfo()
      if (userInfo) {
        userInfo.phoneNumber = phoneNumber
        storageUtils.setUserInfo(userInfo)
      }
      resolve(result)
    })
    .catch(reject)
  })
}

/**
 * 发送验证码
 */
function sendVerificationCode(phoneNumber) {
  return request.post('/auth/send-code', {
    phoneNumber
  })
}

/**
 * 验证手机号验证码
 */
function verifyPhoneCode(phoneNumber, code) {
  return request.post('/auth/verify-code', {
    phoneNumber,
    code
  })
}

module.exports = {
  login,
  performLogin,
  getUserProfile,
  getPhoneNumber,
  checkLoginStatus,
  logout,
  refreshToken,
  requireAuth,
  updateUserInfo,
  bindPhoneNumber,
  sendVerificationCode,
  verifyPhoneCode
}
