// 网络请求封装
const storageUtils = require('./storage')
const app = getApp()

// 请求基础配置
const config = {
  baseUrl: 'https://api.example.com',
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}

// 请求拦截器
function requestInterceptor(options) {
  // 添加token
  const token = storageUtils.getToken()
  if (token) {
    options.header = {
      ...options.header,
      'Authorization': `Bearer ${token}`
    }
  }
  
  // 添加基础URL
  if (!options.url.startsWith('http')) {
    options.url = config.baseUrl + options.url
  }
  
  // 显示加载提示
  if (options.showLoading !== false) {
    wx.showLoading({
      title: options.loadingText || '加载中...',
      mask: true
    })
  }
  
  console.log('请求发送:', options)
  return options
}

// 响应拦截器
function responseInterceptor(response, options) {
  // 隐藏加载提示
  if (options.showLoading !== false) {
    wx.hideLoading()
  }
  
  console.log('响应接收:', response)
  
  const { statusCode, data } = response
  
  // HTTP状态码检查
  if (statusCode >= 200 && statusCode < 300) {
    // 业务状态码检查
    if (data.code === 0 || data.success) {
      return data.data || data
    } else {
      // 业务错误处理
      const errorMsg = data.message || data.msg || '请求失败'
      
      // 特殊错误码处理
      if (data.code === 401) {
        // 登录过期
        handleAuthError()
        return Promise.reject(new Error('登录已过期，请重新登录'))
      }
      
      // 显示错误提示
      if (options.showError !== false) {
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        })
      }
      
      return Promise.reject(new Error(errorMsg))
    }
  } else {
    // HTTP错误处理
    const errorMsg = getHttpErrorMessage(statusCode)
    
    if (options.showError !== false) {
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      })
    }
    
    return Promise.reject(new Error(errorMsg))
  }
}

// 错误拦截器
function errorInterceptor(error, options) {
  // 隐藏加载提示
  if (options.showLoading !== false) {
    wx.hideLoading()
  }
  
  console.error('请求错误:', error)
  
  let errorMsg = '网络请求失败'
  
  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      errorMsg = '请求超时，请检查网络连接'
    } else if (error.errMsg.includes('fail')) {
      errorMsg = '网络连接失败，请检查网络设置'
    }
  }
  
  // 显示错误提示
  if (options.showError !== false) {
    wx.showToast({
      title: errorMsg,
      icon: 'none',
      duration: 2000
    })
  }
  
  return Promise.reject(new Error(errorMsg))
}

// 处理认证错误
function handleAuthError() {
  storageUtils.clearAuth()
  app.globalData.isLogin = false
  app.globalData.userInfo = null
  
  // 跳转到登录页
  wx.reLaunch({
    url: '/pages/login/login'
  })
}

// 获取HTTP错误信息
function getHttpErrorMessage(statusCode) {
  const errorMap = {
    400: '请求参数错误',
    401: '未授权，请重新登录',
    403: '拒绝访问',
    404: '请求的资源不存在',
    405: '请求方法不允许',
    408: '请求超时',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  }
  
  return errorMap[statusCode] || `请求失败 (${statusCode})`
}

// 通用请求方法
function request(options) {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const requestOptions = requestInterceptor({
      timeout: config.timeout,
      header: { ...config.header },
      ...options
    })
    
    wx.request({
      ...requestOptions,
      success: (response) => {
        responseInterceptor(response, options)
          .then(resolve)
          .catch(reject)
      },
      fail: (error) => {
        errorInterceptor(error, options)
          .catch(reject)
      }
    })
  })
}

// GET请求
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

// POST请求
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// 文件上传
function upload(url, filePath, options = {}) {
  return new Promise((resolve, reject) => {
    // 添加token
    const token = storageUtils.getToken()
    const header = { ...options.header }
    if (token) {
      header['Authorization'] = `Bearer ${token}`
    }
    
    // 添加基础URL
    if (!url.startsWith('http')) {
      url = config.baseUrl + url
    }
    
    // 显示加载提示
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '上传中...',
        mask: true
      })
    }
    
    wx.uploadFile({
      url,
      filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header,
      success: (response) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }
        
        try {
          const data = JSON.parse(response.data)
          if (data.code === 0 || data.success) {
            resolve(data.data || data)
          } else {
            const errorMsg = data.message || data.msg || '上传失败'
            if (options.showError !== false) {
              wx.showToast({
                title: errorMsg,
                icon: 'none'
              })
            }
            reject(new Error(errorMsg))
          }
        } catch (error) {
          if (options.showError !== false) {
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
          reject(error)
        }
      },
      fail: (error) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }
        
        if (options.showError !== false) {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
        }
        reject(error)
      }
    })
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  upload,
  config
}
