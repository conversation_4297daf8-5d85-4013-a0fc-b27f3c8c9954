// 验证工具函数

/**
 * 验证手机号
 * @param {string} phone 手机号
 */
function validatePhone(phone) {
  if (!phone) {
    return { valid: false, message: '请输入手机号' }
  }
  
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(phone)) {
    return { valid: false, message: '请输入正确的手机号' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 */
function validateEmail(email) {
  if (!email) {
    return { valid: false, message: '请输入邮箱' }
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return { valid: false, message: '请输入正确的邮箱格式' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证密码
 * @param {string} password 密码
 * @param {object} options 验证选项
 */
function validatePassword(password, options = {}) {
  const {
    minLength = 6,
    maxLength = 20,
    requireNumber = false,
    requireLetter = false,
    requireSpecial = false
  } = options
  
  if (!password) {
    return { valid: false, message: '请输入密码' }
  }
  
  if (password.length < minLength) {
    return { valid: false, message: `密码长度不能少于${minLength}位` }
  }
  
  if (password.length > maxLength) {
    return { valid: false, message: `密码长度不能超过${maxLength}位` }
  }
  
  if (requireNumber && !/\d/.test(password)) {
    return { valid: false, message: '密码必须包含数字' }
  }
  
  if (requireLetter && !/[a-zA-Z]/.test(password)) {
    return { valid: false, message: '密码必须包含字母' }
  }
  
  if (requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return { valid: false, message: '密码必须包含特殊字符' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 */
function validateIdCard(idCard) {
  if (!idCard) {
    return { valid: false, message: '请输入身份证号' }
  }
  
  // 18位身份证号验证
  const idCard18Regex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  // 15位身份证号验证
  const idCard15Regex = /^[1-9]\d{5}\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}$/
  
  if (!idCard18Regex.test(idCard) && !idCard15Regex.test(idCard)) {
    return { valid: false, message: '请输入正确的身份证号' }
  }
  
  // 18位身份证校验码验证
  if (idCard.length === 18) {
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    let sum = 0
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard[i]) * weights[i]
    }
    
    const checkCode = checkCodes[sum % 11]
    if (idCard[17].toUpperCase() !== checkCode) {
      return { valid: false, message: '身份证号校验码错误' }
    }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证银行卡号
 * @param {string} cardNumber 银行卡号
 */
function validateBankCard(cardNumber) {
  if (!cardNumber) {
    return { valid: false, message: '请输入银行卡号' }
  }
  
  const cardRegex = /^\d{16,19}$/
  if (!cardRegex.test(cardNumber)) {
    return { valid: false, message: '银行卡号格式错误' }
  }
  
  // Luhn算法验证
  let sum = 0
  let isEven = false
  
  for (let i = cardNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cardNumber[i])
    
    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }
    
    sum += digit
    isEven = !isEven
  }
  
  if (sum % 10 !== 0) {
    return { valid: false, message: '银行卡号校验失败' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证验证码
 * @param {string} code 验证码
 * @param {number} length 验证码长度
 */
function validateCode(code, length = 6) {
  if (!code) {
    return { valid: false, message: '请输入验证码' }
  }
  
  if (code.length !== length) {
    return { valid: false, message: `验证码应为${length}位` }
  }
  
  const codeRegex = new RegExp(`^\\d{${length}}$`)
  if (!codeRegex.test(code)) {
    return { valid: false, message: '验证码格式错误' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证昵称
 * @param {string} nickname 昵称
 */
function validateNickname(nickname) {
  if (!nickname) {
    return { valid: false, message: '请输入昵称' }
  }
  
  if (nickname.length < 2) {
    return { valid: false, message: '昵称不能少于2个字符' }
  }
  
  if (nickname.length > 20) {
    return { valid: false, message: '昵称不能超过20个字符' }
  }
  
  // 检查是否包含特殊字符
  const specialChars = /[<>'"&]/
  if (specialChars.test(nickname)) {
    return { valid: false, message: '昵称不能包含特殊字符' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证URL
 * @param {string} url URL地址
 */
function validateUrl(url) {
  if (!url) {
    return { valid: false, message: '请输入URL' }
  }
  
  try {
    new URL(url)
    return { valid: true, message: '' }
  } catch (error) {
    return { valid: false, message: '请输入正确的URL格式' }
  }
}

/**
 * 验证搜索关键词
 * @param {string} keyword 搜索关键词
 */
function validateSearchKeyword(keyword) {
  if (!keyword) {
    return { valid: false, message: '请输入搜索关键词' }
  }
  
  const trimmedKeyword = keyword.trim()
  if (!trimmedKeyword) {
    return { valid: false, message: '搜索关键词不能为空' }
  }
  
  if (trimmedKeyword.length > 100) {
    return { valid: false, message: '搜索关键词不能超过100个字符' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证文件大小
 * @param {number} size 文件大小（字节）
 * @param {number} maxSize 最大大小（字节）
 */
function validateFileSize(size, maxSize = 10 * 1024 * 1024) {
  if (!size || size <= 0) {
    return { valid: false, message: '文件大小无效' }
  }
  
  if (size > maxSize) {
    const maxSizeMB = Math.round(maxSize / 1024 / 1024)
    return { valid: false, message: `文件大小不能超过${maxSizeMB}MB` }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证文件类型
 * @param {string} fileName 文件名
 * @param {Array} allowedTypes 允许的文件类型
 */
function validateFileType(fileName, allowedTypes = []) {
  if (!fileName) {
    return { valid: false, message: '文件名无效' }
  }
  
  if (allowedTypes.length === 0) {
    return { valid: true, message: '' }
  }
  
  const fileExtension = fileName.split('.').pop().toLowerCase()
  if (!allowedTypes.includes(fileExtension)) {
    return { valid: false, message: `只允许上传${allowedTypes.join('、')}格式的文件` }
  }
  
  return { valid: true, message: '' }
}

/**
 * 验证表单
 * @param {object} data 表单数据
 * @param {object} rules 验证规则
 */
function validateForm(data, rules) {
  const errors = {}
  let isValid = true
  
  for (const field in rules) {
    const rule = rules[field]
    const value = data[field]
    
    // 必填验证
    if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      errors[field] = rule.message || `${field}不能为空`
      isValid = false
      continue
    }
    
    // 如果不是必填且值为空，跳过其他验证
    if (!rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      continue
    }
    
    // 自定义验证函数
    if (rule.validator && typeof rule.validator === 'function') {
      const result = rule.validator(value)
      if (!result.valid) {
        errors[field] = result.message
        isValid = false
      }
    }
  }
  
  return { valid: isValid, errors }
}

module.exports = {
  validatePhone,
  validateEmail,
  validatePassword,
  validateIdCard,
  validateBankCard,
  validateCode,
  validateNickname,
  validateUrl,
  validateSearchKeyword,
  validateFileSize,
  validateFileType,
  validateForm
}
