// 本地存储封装
const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  SEARCH_HISTORY: 'searchHistory',
  FAVORITES: 'favorites',
  SETTINGS: 'settings'
}

/**
 * 设置存储
 * @param {string} key 存储键
 * @param {any} value 存储值
 * @param {boolean} sync 是否同步存储
 */
function setStorage(key, value, sync = false) {
  try {
    const data = JSON.stringify(value)
    if (sync) {
      wx.setStorageSync(key, data)
    } else {
      return new Promise((resolve, reject) => {
        wx.setStorage({
          key,
          data,
          success: resolve,
          fail: reject
        })
      })
    }
  } catch (error) {
    console.error('存储设置失败:', error)
    throw error
  }
}

/**
 * 获取存储
 * @param {string} key 存储键
 * @param {any} defaultValue 默认值
 * @param {boolean} sync 是否同步获取
 */
function getStorage(key, defaultValue = null, sync = false) {
  try {
    let data
    if (sync) {
      data = wx.getStorageSync(key)
    } else {
      return new Promise((resolve, reject) => {
        wx.getStorage({
          key,
          success: (res) => {
            try {
              const value = JSON.parse(res.data)
              resolve(value)
            } catch (error) {
              resolve(res.data)
            }
          },
          fail: () => {
            resolve(defaultValue)
          }
        })
      })
    }
    
    if (data) {
      return JSON.parse(data)
    }
    return defaultValue
  } catch (error) {
    console.error('存储获取失败:', error)
    return defaultValue
  }
}

/**
 * 删除存储
 * @param {string} key 存储键
 * @param {boolean} sync 是否同步删除
 */
function removeStorage(key, sync = false) {
  try {
    if (sync) {
      wx.removeStorageSync(key)
    } else {
      return new Promise((resolve, reject) => {
        wx.removeStorage({
          key,
          success: resolve,
          fail: reject
        })
      })
    }
  } catch (error) {
    console.error('存储删除失败:', error)
    throw error
  }
}

/**
 * 清空存储
 * @param {boolean} sync 是否同步清空
 */
function clearStorage(sync = false) {
  try {
    if (sync) {
      wx.clearStorageSync()
    } else {
      return new Promise((resolve, reject) => {
        wx.clearStorage({
          success: resolve,
          fail: reject
        })
      })
    }
  } catch (error) {
    console.error('存储清空失败:', error)
    throw error
  }
}

/**
 * 获取存储信息
 */
function getStorageInfo() {
  return new Promise((resolve, reject) => {
    wx.getStorageInfo({
      success: resolve,
      fail: reject
    })
  })
}

// Token相关
function setToken(token) {
  return setStorage(STORAGE_KEYS.TOKEN, token, true)
}

function getToken() {
  return getStorage(STORAGE_KEYS.TOKEN, null, true)
}

function removeToken() {
  return removeStorage(STORAGE_KEYS.TOKEN, true)
}

// 用户信息相关
function setUserInfo(userInfo) {
  return setStorage(STORAGE_KEYS.USER_INFO, userInfo, true)
}

function getUserInfo() {
  return getStorage(STORAGE_KEYS.USER_INFO, null, true)
}

function removeUserInfo() {
  return removeStorage(STORAGE_KEYS.USER_INFO, true)
}

// 搜索历史相关
function getSearchHistory() {
  return getStorage(STORAGE_KEYS.SEARCH_HISTORY, [], true)
}

function addSearchHistory(keyword) {
  if (!keyword || !keyword.trim()) return
  
  const history = getSearchHistory()
  const trimmedKeyword = keyword.trim()
  
  // 移除重复项
  const index = history.indexOf(trimmedKeyword)
  if (index > -1) {
    history.splice(index, 1)
  }
  
  // 添加到开头
  history.unshift(trimmedKeyword)
  
  // 限制数量
  if (history.length > 20) {
    history.splice(20)
  }
  
  setStorage(STORAGE_KEYS.SEARCH_HISTORY, history, true)
}

function removeSearchHistory(keyword) {
  const history = getSearchHistory()
  const index = history.indexOf(keyword)
  if (index > -1) {
    history.splice(index, 1)
    setStorage(STORAGE_KEYS.SEARCH_HISTORY, history, true)
  }
}

function clearSearchHistory() {
  return removeStorage(STORAGE_KEYS.SEARCH_HISTORY, true)
}

// 收藏相关
function getFavorites() {
  return getStorage(STORAGE_KEYS.FAVORITES, [], true)
}

function addFavorite(item) {
  const favorites = getFavorites()
  
  // 检查是否已收藏
  const exists = favorites.some(fav => fav.id === item.id)
  if (exists) return false
  
  // 添加收藏时间
  const favoriteItem = {
    ...item,
    favoriteTime: Date.now()
  }
  
  favorites.unshift(favoriteItem)
  setStorage(STORAGE_KEYS.FAVORITES, favorites, true)
  return true
}

function removeFavorite(id) {
  const favorites = getFavorites()
  const index = favorites.findIndex(item => item.id === id)
  if (index > -1) {
    favorites.splice(index, 1)
    setStorage(STORAGE_KEYS.FAVORITES, favorites, true)
    return true
  }
  return false
}

function isFavorite(id) {
  const favorites = getFavorites()
  return favorites.some(item => item.id === id)
}

function clearFavorites() {
  return removeStorage(STORAGE_KEYS.FAVORITES, true)
}

// 设置相关
function getSettings() {
  return getStorage(STORAGE_KEYS.SETTINGS, {
    autoSearch: true,
    searchSuggestion: true,
    nightMode: false,
    fontSize: 'medium'
  }, true)
}

function setSetting(key, value) {
  const settings = getSettings()
  settings[key] = value
  setStorage(STORAGE_KEYS.SETTINGS, settings, true)
}

function getSetting(key, defaultValue = null) {
  const settings = getSettings()
  return settings[key] !== undefined ? settings[key] : defaultValue
}

// 清除认证信息
function clearAuth() {
  removeToken()
  removeUserInfo()
}

// 清除用户数据
function clearUserData() {
  clearSearchHistory()
  clearFavorites()
  removeStorage(STORAGE_KEYS.SETTINGS, true)
}

// 清除所有数据
function clearAllData() {
  clearAuth()
  clearUserData()
}

module.exports = {
  // 基础存储方法
  setStorage,
  getStorage,
  removeStorage,
  clearStorage,
  getStorageInfo,
  
  // Token相关
  setToken,
  getToken,
  removeToken,
  
  // 用户信息相关
  setUserInfo,
  getUserInfo,
  removeUserInfo,
  
  // 搜索历史相关
  getSearchHistory,
  addSearchHistory,
  removeSearchHistory,
  clearSearchHistory,
  
  // 收藏相关
  getFavorites,
  addFavorite,
  removeFavorite,
  isFavorite,
  clearFavorites,
  
  // 设置相关
  getSettings,
  setSetting,
  getSetting,
  
  // 清除方法
  clearAuth,
  clearUserData,
  clearAllData,
  
  // 常量
  STORAGE_KEYS
}
