// 格式化工具函数

/**
 * 格式化时间
 * @param {number|string|Date} time 时间
 * @param {string} format 格式化模板
 */
function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return ''
  
  const date = new Date(time)
  if (isNaN(date.getTime())) return ''
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  const second = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 格式化相对时间
 * @param {number|string|Date} time 时间
 */
function formatRelativeTime(time) {
  if (!time) return ''
  
  const date = new Date(time)
  if (isNaN(date.getTime())) return ''
  
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @param {number} decimals 小数位数
 */
function formatFileSize(size, decimals = 2) {
  if (!size || size === 0) return '0 B'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  
  const i = Math.floor(Math.log(size) / Math.log(k))
  
  return parseFloat((size / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 */
function formatNumber(num, decimals = 0) {
  if (isNaN(num)) return '0'
  
  if (num >= 100000000) {
    return (num / 100000000).toFixed(decimals) + '亿'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(decimals) + '万'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(decimals) + 'k'
  } else {
    return num.toString()
  }
}

/**
 * 格式化手机号
 * @param {string} phone 手机号
 */
function formatPhone(phone) {
  if (!phone) return ''
  
  const phoneStr = phone.toString()
  if (phoneStr.length === 11) {
    return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
  }
  return phone
}

/**
 * 格式化身份证号
 * @param {string} idCard 身份证号
 */
function formatIdCard(idCard) {
  if (!idCard) return ''
  
  const idStr = idCard.toString()
  if (idStr.length === 18) {
    return idStr.replace(/(\d{6})(\d{8})(\d{4})/, '$1********$3')
  } else if (idStr.length === 15) {
    return idStr.replace(/(\d{6})(\d{6})(\d{3})/, '$1******$3')
  }
  return idCard
}

/**
 * 格式化银行卡号
 * @param {string} cardNumber 银行卡号
 */
function formatBankCard(cardNumber) {
  if (!cardNumber) return ''
  
  const cardStr = cardNumber.toString()
  if (cardStr.length >= 16) {
    return cardStr.replace(/(\d{4})(\d+)(\d{4})/, '$1 **** **** $3')
  }
  return cardNumber
}

/**
 * 格式化价格
 * @param {number} price 价格
 * @param {string} currency 货币符号
 * @param {number} decimals 小数位数
 */
function formatPrice(price, currency = '¥', decimals = 2) {
  if (isNaN(price)) return currency + '0.00'
  
  return currency + Number(price).toFixed(decimals)
}

/**
 * 格式化百分比
 * @param {number} value 数值
 * @param {number} total 总数
 * @param {number} decimals 小数位数
 */
function formatPercent(value, total, decimals = 1) {
  if (!total || total === 0) return '0%'
  
  const percent = (value / total) * 100
  return percent.toFixed(decimals) + '%'
}

/**
 * 格式化网盘类型
 * @param {string} diskType 网盘类型
 */
function formatDiskType(diskType) {
  const typeMap = {
    'quark': '夸克网盘',
    'aliyun': '阿里云盘',
    'baidu': '百度网盘',
    '115': '115网盘',
    'lanzou': '蓝奏云',
    'onedrive': 'OneDrive',
    'googledrive': 'Google Drive'
  }
  
  return typeMap[diskType] || diskType
}

/**
 * 格式化链接显示
 * @param {string} url 链接
 * @param {number} maxLength 最大长度
 */
function formatUrl(url, maxLength = 50) {
  if (!url) return ''
  
  if (url.length <= maxLength) {
    return url
  }
  
  const start = url.substring(0, maxLength / 2)
  const end = url.substring(url.length - maxLength / 2)
  return start + '...' + end
}

/**
 * 格式化搜索关键词高亮
 * @param {string} text 文本
 * @param {string} keyword 关键词
 */
function formatHighlight(text, keyword) {
  if (!text || !keyword) return text
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span class="highlight">$1</span>')
}

/**
 * 格式化标签
 * @param {Array} tags 标签数组
 * @param {number} maxCount 最大显示数量
 */
function formatTags(tags, maxCount = 3) {
  if (!Array.isArray(tags)) return []
  
  if (tags.length <= maxCount) {
    return tags
  }
  
  return [...tags.slice(0, maxCount), `+${tags.length - maxCount}`]
}

/**
 * 格式化用户名
 * @param {string} username 用户名
 * @param {number} maxLength 最大长度
 */
function formatUsername(username, maxLength = 10) {
  if (!username) return '匿名用户'
  
  if (username.length <= maxLength) {
    return username
  }
  
  return username.substring(0, maxLength) + '...'
}

/**
 * 格式化描述文本
 * @param {string} description 描述
 * @param {number} maxLength 最大长度
 */
function formatDescription(description, maxLength = 100) {
  if (!description) return ''
  
  if (description.length <= maxLength) {
    return description
  }
  
  return description.substring(0, maxLength) + '...'
}

/**
 * 格式化版本号
 * @param {string} version 版本号
 */
function formatVersion(version) {
  if (!version) return ''
  
  // 确保版本号格式为 x.x.x
  const parts = version.split('.')
  while (parts.length < 3) {
    parts.push('0')
  }
  
  return parts.slice(0, 3).join('.')
}

module.exports = {
  formatTime,
  formatRelativeTime,
  formatFileSize,
  formatNumber,
  formatPhone,
  formatIdCard,
  formatBankCard,
  formatPrice,
  formatPercent,
  formatDiskType,
  formatUrl,
  formatHighlight,
  formatTags,
  formatUsername,
  formatDescription,
  formatVersion
}
