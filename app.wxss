/**app.wxss**/
@import './styles/variables.wxss';
@import './styles/common.wxss';

/* 全局样式重置 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 通用容器 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.page-container {
  background-color: #fff;
  min-height: 100vh;
}

/* 通用布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 通用间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pt-30 { padding-top: 30rpx; }
.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pb-30 { padding-bottom: 30rpx; }
.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }

/* 通用文本 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-muted { color: var(--text-muted); }

.font-12 { font-size: 24rpx; }
.font-14 { font-size: 28rpx; }
.font-16 { font-size: 32rpx; }
.font-18 { font-size: 36rpx; }
.font-20 { font-size: 40rpx; }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

/* 通用背景 */
.bg-white { background-color: #fff; }
.bg-gray { background-color: #f5f5f5; }
.bg-primary { background-color: var(--primary-color); }

/* 通用边框 */
.border { border: 1rpx solid #e8e8e8; }
.border-top { border-top: 1rpx solid #e8e8e8; }
.border-bottom { border-bottom: 1rpx solid #e8e8e8; }
.border-left { border-left: 1rpx solid #e8e8e8; }
.border-right { border-right: 1rpx solid #e8e8e8; }

.border-radius { border-radius: 8rpx; }
.border-radius-small { border-radius: 4rpx; }
.border-radius-large { border-radius: 16rpx; }

/* 通用阴影 */
.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-light {
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
}

.btn-small {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 24rpx 48rpx;
  font-size: 32rpx;
}

/* 通用卡片 */
.card {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 通用列表 */
.list-item {
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

/* 通用图片 */
.img-responsive {
  width: 100%;
  height: auto;
}

.img-circle {
  border-radius: 50%;
}

/* 通用隐藏 */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

/* 通用定位 */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

/* 通用层级 */
.z-1 { z-index: 1; }
.z-10 { z-index: 10; }
.z-100 { z-index: 100; }
.z-1000 { z-index: 1000; }

/* 通用宽高 */
.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* 通用溢出 */
.overflow-hidden {
  overflow: hidden;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 通用动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
